// Comprehensive diagnostic script for Visual Complexity Analyzer image upload pipeline
// Run this in the browser console to diagnose image upload and display issues

console.log('🔍 Visual Complexity Analyzer - Image Upload Pipeline Diagnostic');

async function testImageUploadPipeline() {
  console.log('\n🚀 Starting Complete Image Upload Pipeline Test...');
  
  try {
    // Import required services
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // 1. Check authentication
    console.log('\n🔐 Step 1: Authentication Check...');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    if (authError || !user || !session) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      hasAccessToken: !!session.access_token
    });
    
    // 2. Create test image file
    console.log('\n🖼️ Step 2: Creating Test Image...');
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 100, 100);
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '20px Arial';
      ctx.fillText('TEST', 25, 55);
    }
    
    const testImageBlob = await new Promise((resolve) => {
      canvas.toBlob((blob) => resolve(blob), 'image/png');
    });
    
    const testFile = new File([testImageBlob], 'diagnostic-test.png', {
      type: 'image/png'
    });
    
    console.log('✅ Test image created:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });
    
    // 3. Test direct image upload
    console.log('\n📤 Step 3: Testing Direct Image Upload...');
    let uploadedPath = null;
    try {
      uploadedPath = await designAnalysisService.uploadImage(testFile, user.id);
      console.log('✅ Image upload successful:', uploadedPath);
    } catch (uploadError) {
      console.error('❌ Image upload failed:', uploadError);
      return false;
    }
    
    // 4. Verify file exists in storage
    console.log('\n🔍 Step 4: Verifying File in Storage...');
    const fileExists = await designAnalysisService.checkImageExists(uploadedPath);
    console.log('File exists check:', fileExists);
    
    // 5. Test image retrieval
    console.log('\n📥 Step 5: Testing Image Retrieval...');
    const imageUrl = await designAnalysisService.getImageUrl(uploadedPath);
    console.log('Image URL generation:', {
      success: !!imageUrl,
      urlType: imageUrl ? (imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL') : 'Failed',
      urlPreview: imageUrl ? imageUrl.substring(0, 50) + '...' : 'None'
    });
    
    // 6. Test complete saveAnalysis with image
    console.log('\n💾 Step 6: Testing Complete saveAnalysis with Image...');
    const testAnalysisData = {
      user_id: user.id,
      original_filename: 'diagnostic-test-analysis.png',
      file_size: testFile.size,
      file_type: testFile.type,
      tool_type: 'visual-complexity',
      analysis_version: '1.0',
      overall_score: 75,
      complexity_scores: {
        visual_density: 70,
        color_complexity: 80,
        layout_complexity: 75
      },
      analysis_areas: [
        {
          area_id: 'test-area',
          coordinates: { x: 0, y: 0, width: 100, height: 100 },
          complexity_score: 75,
          description: 'Test area for diagnostic'
        }
      ],
      recommendations: ['This is a test analysis'],
      ai_analysis_summary: 'Diagnostic test analysis',
      gemini_analysis: 'Test analysis from diagnostic script',
      agent_message: 'Diagnostic test completed',
      visuai_insights: 'Test insights',
      analysis_duration_ms: 1000,
      status: 'completed'
    };
    
    let savedAnalysis = null;
    try {
      savedAnalysis = await designAnalysisService.saveAnalysis(testAnalysisData, testFile);
      console.log('✅ Analysis saved successfully:', {
        id: savedAnalysis.id,
        file_url: savedAnalysis.file_url,
        original_filename: savedAnalysis.original_filename
      });
    } catch (saveError) {
      console.error('❌ Analysis save failed:', saveError);
      return false;
    }
    
    // 7. Verify database entry
    console.log('\n🗄️ Step 7: Verifying Database Entry...');
    const { data: dbAnalysis, error: dbError } = await supabase
      .from('design_analyses')
      .select('id, file_url, original_filename, user_id')
      .eq('id', savedAnalysis.id)
      .single();
    
    if (dbError) {
      console.error('❌ Database verification failed:', dbError);
      return false;
    }
    
    console.log('✅ Database verification successful:', {
      id: dbAnalysis.id,
      file_url: dbAnalysis.file_url,
      file_url_is_null: dbAnalysis.file_url === null,
      original_filename: dbAnalysis.original_filename
    });
    
    // 8. Test image retrieval from saved analysis
    if (dbAnalysis.file_url) {
      console.log('\n🖼️ Step 8: Testing Image Retrieval from Saved Analysis...');
      const savedImageUrl = await designAnalysisService.getImageUrl(dbAnalysis.file_url);
      console.log('Saved image URL generation:', {
        success: !!savedImageUrl,
        urlType: savedImageUrl ? (savedImageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL') : 'Failed'
      });
      
      if (savedImageUrl && savedImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(savedImageUrl);
      }
    } else {
      console.error('❌ Step 8 FAILED: file_url is null in database - this is the root cause!');
    }
    
    // 9. Cleanup test files
    console.log('\n🧹 Step 9: Cleanup...');
    try {
      // Clean up uploaded test file
      if (uploadedPath) {
        await supabase.storage
          .from('design-analysis-images')
          .remove([uploadedPath]);
        console.log('✅ Test file cleaned up from storage');
      }
      
      // Clean up test analysis
      if (savedAnalysis) {
        await supabase
          .from('design_analyses')
          .delete()
          .eq('id', savedAnalysis.id);
        console.log('✅ Test analysis cleaned up from database');
      }
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup failed:', cleanupError);
    }
    
    // Clean up object URLs
    if (imageUrl && imageUrl.startsWith('blob:')) {
      URL.revokeObjectURL(imageUrl);
    }
    
    // 10. Summary
    console.log('\n📋 DIAGNOSTIC SUMMARY:');
    console.log('='.repeat(50));
    console.log('Authentication:', '✅ Working');
    console.log('Image Upload:', uploadedPath ? '✅ Working' : '❌ Failed');
    console.log('Storage Verification:', fileExists ? '✅ Working' : '❌ Failed');
    console.log('Image Retrieval:', imageUrl ? '✅ Working' : '❌ Failed');
    console.log('Analysis Save:', savedAnalysis ? '✅ Working' : '❌ Failed');
    console.log('Database file_url:', dbAnalysis?.file_url ? '✅ Saved' : '❌ NULL - ROOT CAUSE!');
    
    if (!dbAnalysis?.file_url) {
      console.log('\n🚨 ROOT CAUSE IDENTIFIED:');
      console.log('- Images are uploading successfully to Supabase Storage');
      console.log('- Analysis data is saving to database');
      console.log('- BUT file_url is not being saved to database');
      console.log('- This suggests an issue in the saveAnalysis method');
      console.log('- The uploaded file path is not being properly assigned to finalAnalysisData.file_url');
    }
    
    return {
      uploadWorking: !!uploadedPath,
      storageWorking: fileExists,
      retrievalWorking: !!imageUrl,
      saveWorking: !!savedAnalysis,
      fileUrlSaved: !!dbAnalysis?.file_url,
      rootCause: !dbAnalysis?.file_url ? 'file_url not saved to database' : null
    };
    
  } catch (error) {
    console.error('💥 Diagnostic test failed:', error);
    return false;
  }
}

async function testExistingAnalysisImages() {
  console.log('\n🔍 Testing Existing Analysis Images...');
  
  try {
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.warn('⚠️ No authenticated user');
      return false;
    }
    
    // Get user's analyses
    const analyses = await designAnalysisService.getUserAnalyses(user.id, { limit: 5 });
    console.log(`Found ${analyses.length} analyses for user`);
    
    // Check storage for files that might exist but aren't linked
    const { data: storageFiles, error: storageError } = await supabase.storage
      .from('design-analysis-images')
      .list(user.id, { limit: 10 });
    
    if (storageError) {
      console.error('❌ Storage list failed:', storageError);
      return false;
    }
    
    console.log(`Found ${storageFiles?.length || 0} files in storage for user`);
    
    if (storageFiles && storageFiles.length > 0) {
      console.log('📁 Files in storage:');
      storageFiles.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.name} (${file.metadata?.size || 'unknown size'})`);
      });
      
      // Test retrieving the first file
      const firstFile = storageFiles[0];
      const filePath = `${user.id}/${firstFile.name}`;
      console.log(`\n🧪 Testing retrieval of: ${filePath}`);
      
      const imageUrl = await designAnalysisService.getImageUrl(filePath);
      console.log('Retrieval result:', {
        success: !!imageUrl,
        urlType: imageUrl ? (imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL') : 'Failed'
      });
      
      if (imageUrl && imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Existing analysis test failed:', error);
    return false;
  }
}

// Auto-run the diagnostic
console.log('🚀 Starting automatic diagnostic...');
testImageUploadPipeline().then(result => {
  console.log('\n🏁 Diagnostic completed. Result:', result);
  
  // Also test existing files
  return testExistingAnalysisImages();
}).then(() => {
  console.log('\n✅ All diagnostics completed');
}).catch(error => {
  console.error('💥 Diagnostic failed:', error);
});

// Export functions for manual testing
window.diagnoseImagePipeline = {
  testImageUploadPipeline,
  testExistingAnalysisImages
};

console.log('\n📝 Available diagnostic functions:');
console.log('- window.diagnoseImagePipeline.testImageUploadPipeline()');
console.log('- window.diagnoseImagePipeline.testExistingAnalysisImages()');
