/**
 * Quick Upload Test - Immediate execution
 * Copy and paste this into browser console on Visual Complexity Analyzer page
 */

console.log('🚀 QUICK UPLOAD TEST STARTING...');

(async function quickTest() {
  try {
    // Step 1: Check environment
    console.log('🔍 Checking environment...');
    
    if (!window.supabase) {
      console.error('❌ Supabase not available');
      return;
    }
    
    if (!window.designAnalysisService) {
      console.error('❌ designAnalysisService not available');
      console.log('Available window properties:', Object.keys(window).filter(k => k.includes('design') || k.includes('service')));
      return;
    }
    
    console.log('✅ Environment OK');
    
    // Step 2: Check authentication
    console.log('🔐 Checking authentication...');
    const { data: { user }, error: authError } = await window.supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return;
    }
    
    console.log('✅ Authenticated as:', user.email);
    
    // Step 3: Test direct upload
    console.log('📤 Testing direct upload...');
    
    const testContent = `test-${Date.now()}`;
    const testBlob = new Blob([testContent], { type: 'text/plain' });
    const testFile = new File([testBlob], 'quick-test.txt', { type: 'text/plain' });
    const fileName = `${user.id}/quick-test-${Date.now()}.txt`;
    
    const { data, error } = await window.supabase.storage
      .from('design-analysis-images')
      .upload(fileName, testFile, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) {
      console.error('❌ Direct upload failed:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        status: error.status,
        details: error.details
      });
      
      if (error.message.includes('row-level security policy')) {
        console.error('🚨 RLS POLICY VIOLATION - The fix did not work!');
      }
      
      return;
    }
    
    console.log('✅ Direct upload successful:', data.path);
    
    // Step 4: Test service upload
    console.log('🔧 Testing service upload...');
    
    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = 5;
    canvas.height = 5;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 5, 5);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const imageFile = new File([blob], 'service-test.png', { type: 'image/png' });
    
    try {
      const serviceResult = await window.designAnalysisService.uploadImage(imageFile, user.id);
      
      if (!serviceResult) {
        console.error('❌ Service upload returned null');
      } else {
        console.log('✅ Service upload successful:', serviceResult);
        
        // Clean up service upload
        await window.supabase.storage
          .from('design-analysis-images')
          .remove([serviceResult]);
      }
    } catch (serviceError) {
      console.error('❌ Service upload failed:', serviceError);
      console.error('Service error details:', {
        message: serviceError.message,
        stack: serviceError.stack
      });
    }
    
    // Clean up direct upload
    await window.supabase.storage
      .from('design-analysis-images')
      .remove([fileName]);
    
    console.log('🧹 Cleanup completed');
    
    console.log('🎯 QUICK TEST COMPLETED');
    
  } catch (error) {
    console.error('💥 Quick test failed:', error);
  }
})();

console.log('📋 Quick test script executed');
