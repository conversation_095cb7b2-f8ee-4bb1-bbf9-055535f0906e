/**
 * Direct test to create a mood board and verify the complete flow
 * Run this in the browser console on the mood board page
 */

async function createAndTestMoodBoard() {
  console.log('🎨 Creating Test Mood Board and Testing Complete Flow...\n');
  
  try {
    // Step 1: Check authentication
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.error('❌ Authentication failed:', authError);
      return;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      hasToken: !!session.access_token
    });
    
    // Step 2: Create test mood board data
    const timestamp = new Date().toLocaleTimeString();
    const testMoodBoard = {
      title: `Demo Mood Board ${timestamp}`,
      description: 'A beautiful mood board showcasing the enhanced UI with improved animations, hover effects, and polished design elements.',
      tags: ['demo', 'ui-enhanced', 'polished', 'test'],
      tldraw_data: {
        shapes: [
          {
            id: 'title-shape',
            type: 'text',
            props: {
              text: '🎨 Enhanced UI Demo',
              color: 'blue',
              size: 'xl'
            },
            x: 50,
            y: 50
          },
          {
            id: 'feature-1',
            type: 'text',
            props: {
              text: '✨ Smooth Animations',
              color: 'green',
              size: 'large'
            },
            x: 50,
            y: 150
          },
          {
            id: 'feature-2',
            type: 'text',
            props: {
              text: '🎯 Enhanced Hover Effects',
              color: 'purple',
              size: 'large'
            },
            x: 50,
            y: 250
          },
          {
            id: 'feature-3',
            type: 'text',
            props: {
              text: '🚀 Polished Empty States',
              color: 'orange',
              size: 'large'
            },
            x: 50,
            y: 350
          }
        ],
        bindings: [],
        assets: []
      },
      is_public: false,
      collaboration_enabled: false
    };
    
    console.log('📝 Creating mood board:', testMoodBoard.title);
    
    // Step 3: Make API call to create mood board
    console.log('🔑 Using JWT token:', session.access_token.substring(0, 50) + '...');

    const response = await fetch('http://localhost:8000/api/moodboard/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(testMoodBoard)
    });
    
    console.log('📡 API Response Status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }
    
    const result = await response.json();
    console.log('📄 API Result:', result);
    
    if (result.success) {
      console.log('✅ Mood board created successfully!');
      console.log('🆔 Created mood board ID:', result.data?.id);
      
      // Step 4: Wait and refresh to see the new mood board
      console.log('🔄 Refreshing page in 3 seconds to display the new mood board...');
      setTimeout(() => {
        window.location.reload();
      }, 3000);
      
      return result.data;
    } else {
      console.error('❌ Failed to create mood board:', result.message);
    }
    
  } catch (error) {
    console.error('❌ Error in test:', error);
  }
}

// Auto-run the test
console.log('🚀 Starting mood board creation test...');
createAndTestMoodBoard();
