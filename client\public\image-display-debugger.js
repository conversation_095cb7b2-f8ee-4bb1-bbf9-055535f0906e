/**
 * Comprehensive Image Display Debugger for Emma Studio
 * This tool helps diagnose and fix image display issues in the Visual Complexity Analyzer
 * 
 * Usage: Open browser console on Visual Complexity Analyzer page and run:
 * const debugger = new ImageDisplayDebugger(); await debugger.runFullDiagnostic();
 */

console.log('🔧 Image Display Debugger Loaded');

class ImageDisplayDebugger {
  constructor() {
    this.results = {};
    this.errors = [];
    this.supabase = null;
    this.designAnalysisService = null;
  }

  async init() {
    try {
      // Import required modules
      this.supabase = (await import('/src/lib/supabase.ts')).supabase;
      this.designAnalysisService = (await import('/src/services/designAnalysisService.ts')).designAnalysisService;
      console.log('✅ Modules imported successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to import modules:', error);
      this.errors.push(`Module import failed: ${error.message}`);
      return false;
    }
  }

  async checkAuthentication() {
    console.log('\n🔐 Checking Authentication...');
    
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();
      
      if (error) {
        console.error('❌ Auth error:', error);
        this.errors.push(`Authentication error: ${error.message}`);
        return false;
      }
      
      if (!user) {
        console.error('❌ No authenticated user');
        this.errors.push('No authenticated user found');
        return false;
      }
      
      console.log('✅ User authenticated:', {
        id: user.id,
        email: user.email,
        role: user.role
      });
      
      this.results.auth = {
        userId: user.id,
        email: user.email,
        authenticated: true
      };
      
      return true;
    } catch (error) {
      console.error('❌ Authentication check failed:', error);
      this.errors.push(`Auth check failed: ${error.message}`);
      return false;
    }
  }

  async checkStorageAccess() {
    console.log('\n📦 Checking Storage Access...');
    
    try {
      // Test bucket access
      const { data: buckets, error: bucketError } = await this.supabase.storage.listBuckets();
      
      if (bucketError) {
        console.error('❌ Bucket list error:', bucketError);
        this.errors.push(`Bucket access error: ${bucketError.message}`);
        return false;
      }
      
      const designBucket = buckets.find(b => b.name === 'design-analysis-images');
      if (!designBucket) {
        console.error('❌ design-analysis-images bucket not found');
        this.errors.push('design-analysis-images bucket not found');
        return false;
      }
      
      console.log('✅ Bucket found:', {
        name: designBucket.name,
        public: designBucket.public,
        fileLimit: designBucket.file_size_limit,
        allowedTypes: designBucket.allowed_mime_types
      });
      
      // Test user folder access
      const { data: files, error: listError } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, { limit: 5 });
      
      if (listError) {
        console.error('❌ User folder access error:', listError);
        this.errors.push(`User folder access error: ${listError.message}`);
        return false;
      }
      
      console.log('✅ User folder accessible:', {
        fileCount: files.length,
        files: files.map(f => ({ name: f.name, size: f.metadata?.size }))
      });
      
      this.results.storage = {
        bucketExists: true,
        bucketPublic: designBucket.public,
        userFolderAccessible: true,
        userFileCount: files.length,
        userFiles: files
      };
      
      return true;
    } catch (error) {
      console.error('❌ Storage access check failed:', error);
      this.errors.push(`Storage access failed: ${error.message}`);
      return false;
    }
  }

  async testImageRetrieval() {
    console.log('\n🖼️ Testing Image Retrieval...');
    
    if (!this.results.storage?.userFiles?.length) {
      console.log('⚠️ No user files found to test');
      return true;
    }
    
    const testFile = this.results.storage.userFiles[0];
    const filePath = `${this.results.auth.userId}/${testFile.name}`;
    
    console.log('🧪 Testing file:', filePath);
    
    try {
      // Test 1: Direct download method (current implementation)
      console.log('\n📥 Method 1: Direct Download (Current)');
      const { data: fileBlob, error: downloadError } = await this.supabase.storage
        .from('design-analysis-images')
        .download(filePath);
      
      if (downloadError) {
        console.error('❌ Download failed:', downloadError);
        this.errors.push(`Download failed: ${downloadError.message}`);
      } else {
        console.log('✅ Download successful:', {
          size: fileBlob.size,
          type: fileBlob.type
        });
        
        // Test object URL creation
        const objectUrl = URL.createObjectURL(fileBlob);
        console.log('✅ Object URL created:', objectUrl.substring(0, 50) + '...');
        
        // Test if the blob URL works in an image element
        const testResult = await this.testImageLoad(objectUrl);
        console.log('🧪 Image load test:', testResult ? '✅ Success' : '❌ Failed');
        
        // Clean up
        URL.revokeObjectURL(objectUrl);
        
        this.results.directDownload = {
          success: true,
          blobSize: fileBlob.size,
          blobType: fileBlob.type,
          objectUrlWorks: testResult
        };
      }
      
      // Test 2: Signed URL method
      console.log('\n🔐 Method 2: Signed URL');
      const { data: signedData, error: signedError } = await this.supabase.storage
        .from('design-analysis-images')
        .createSignedUrl(filePath, 3600);
      
      if (signedError) {
        console.error('❌ Signed URL failed:', signedError);
        this.errors.push(`Signed URL failed: ${signedError.message}`);
      } else {
        console.log('✅ Signed URL created:', signedData.signedUrl.substring(0, 50) + '...');
        
        // Test if the signed URL works
        const testResult = await this.testImageLoad(signedData.signedUrl);
        console.log('🧪 Signed URL test:', testResult ? '✅ Success' : '❌ Failed');
        
        this.results.signedUrl = {
          success: true,
          url: signedData.signedUrl,
          urlWorks: testResult
        };
      }
      
      // Test 3: Public URL method (should fail for private bucket)
      console.log('\n📡 Method 3: Public URL (Expected to fail)');
      const { data: { publicUrl } } = this.supabase.storage
        .from('design-analysis-images')
        .getPublicUrl(filePath);
      
      console.log('🔗 Public URL generated:', publicUrl.substring(0, 50) + '...');
      
      const testResult = await this.testImageLoad(publicUrl);
      console.log('🧪 Public URL test:', testResult ? '✅ Success (unexpected)' : '❌ Failed (expected)');
      
      this.results.publicUrl = {
        url: publicUrl,
        urlWorks: testResult
      };
      
      return true;
    } catch (error) {
      console.error('❌ Image retrieval test failed:', error);
      this.errors.push(`Image retrieval test failed: ${error.message}`);
      return false;
    }
  }

  async testImageLoad(url) {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
      
      // Timeout after 5 seconds
      setTimeout(() => resolve(false), 5000);
    });
  }

  async testServiceMethod() {
    console.log('\n🔧 Testing Service Method...');
    
    if (!this.results.storage?.userFiles?.length) {
      console.log('⚠️ No user files found to test service method');
      return true;
    }
    
    const testFile = this.results.storage.userFiles[0];
    const filePath = `${this.results.auth.userId}/${testFile.name}`;
    
    try {
      console.log('🧪 Testing designAnalysisService.getImageUrl()...');
      const serviceUrl = await this.designAnalysisService.getImageUrl(filePath);
      
      if (serviceUrl) {
        console.log('✅ Service method successful:', {
          type: serviceUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL',
          url: serviceUrl.substring(0, 50) + '...'
        });
        
        const testResult = await this.testImageLoad(serviceUrl);
        console.log('🧪 Service URL test:', testResult ? '✅ Success' : '❌ Failed');
        
        this.results.serviceMethod = {
          success: true,
          url: serviceUrl,
          urlWorks: testResult
        };
        
        // Clean up if it's a blob URL
        if (serviceUrl.startsWith('blob:')) {
          URL.revokeObjectURL(serviceUrl);
        }
      } else {
        console.error('❌ Service method returned null');
        this.errors.push('Service method returned null');
        this.results.serviceMethod = { success: false };
      }
      
      return true;
    } catch (error) {
      console.error('❌ Service method test failed:', error);
      this.errors.push(`Service method failed: ${error.message}`);
      this.results.serviceMethod = { success: false, error: error.message };
      return false;
    }
  }

  async checkCORSConfiguration() {
    console.log('\n🌐 Checking CORS Configuration...');
    
    try {
      // Test CORS by making a fetch request to the Supabase storage endpoint
      const testUrl = `https://pthewpjbegkgomvyhkin.supabase.co/storage/v1/object/design-analysis-images/test`;
      
      const response = await fetch(testUrl, {
        method: 'HEAD',
        mode: 'cors'
      });
      
      console.log('✅ CORS test response:', {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      });
      
      this.results.cors = {
        accessible: response.status !== 0,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };
      
      return true;
    } catch (error) {
      console.error('❌ CORS test failed:', error);
      this.errors.push(`CORS test failed: ${error.message}`);
      this.results.cors = { accessible: false, error: error.message };
      return false;
    }
  }

  async runFullDiagnostic() {
    console.log('🚀 Starting Full Image Display Diagnostic...\n');
    
    const initSuccess = await this.init();
    if (!initSuccess) {
      console.log('❌ Diagnostic failed - could not initialize');
      return this.generateReport();
    }
    
    await this.checkAuthentication();
    await this.checkStorageAccess();
    await this.testImageRetrieval();
    await this.testServiceMethod();
    await this.checkCORSConfiguration();
    
    return this.generateReport();
  }

  generateReport() {
    console.log('\n📊 DIAGNOSTIC REPORT');
    console.log('='.repeat(50));
    
    console.log('\n✅ SUCCESSFUL TESTS:');
    Object.entries(this.results).forEach(([test, result]) => {
      if (result.success !== false) {
        console.log(`  ✓ ${test}`);
      }
    });
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach(error => console.log(`  ✗ ${error}`));
    }
    
    console.log('\n🔧 RECOMMENDATIONS:');
    
    if (this.results.directDownload?.success && this.results.directDownload?.objectUrlWorks) {
      console.log('  ✓ Direct download method is working correctly');
    } else {
      console.log('  ⚠️ Direct download method has issues - check RLS policies');
    }
    
    if (this.results.serviceMethod?.success && this.results.serviceMethod?.urlWorks) {
      console.log('  ✓ Service method is working correctly');
    } else {
      console.log('  ⚠️ Service method has issues - check implementation');
    }
    
    if (!this.results.publicUrl?.urlWorks) {
      console.log('  ✓ Public URL correctly fails for private bucket');
    } else {
      console.log('  ⚠️ Public URL unexpectedly works - bucket may not be private');
    }
    
    console.log('\n📋 Full Results:', this.results);
    
    return {
      success: this.errors.length === 0,
      results: this.results,
      errors: this.errors
    };
  }
}

// Make it globally available
window.ImageDisplayDebugger = ImageDisplayDebugger;

// Quick test function
window.quickImageTest = async function() {
  console.log('🚀 Running Quick Image Test...');
  const debugger = new ImageDisplayDebugger();
  const result = await debugger.runFullDiagnostic();

  if (result.success) {
    console.log('✅ All tests passed! Image display should be working.');
  } else {
    console.log('❌ Some tests failed. Check the detailed results above.');
    console.log('🔧 Common fixes:');
    console.log('  1. Make sure you are logged in');
    console.log('  2. Upload an image first in the Visual Complexity Analyzer');
    console.log('  3. Check your internet connection');
    console.log('  4. Try refreshing the page');
  }

  return result;
};

console.log('🎯 Ready! Run one of these commands:');
console.log('  quickImageTest() - Quick automated test');
console.log('  const debugger = new ImageDisplayDebugger(); await debugger.runFullDiagnostic(); - Full diagnostic');
