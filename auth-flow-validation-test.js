/**
 * Authentication and Authorization Flow Validation Test
 * Tests JWT token validation, user_id filtering, and potential security vulnerabilities
 * 
 * Run this in the browser console on: http://localhost:3002
 */

async function validateAuthFlow() {
  console.log('🔐 AUTHENTICATION & AUTHORIZATION FLOW VALIDATION');
  console.log('='.repeat(60));
  
  const testResults = {
    jwtValidation: false,
    tokenExpiry: false,
    userIdConsistency: false,
    sessionSecurity: false,
    backendAuth: false,
    overallAuthScore: 0
  };

  // Test 1: JWT Token Validation
  console.log('\n🎫 TEST 1: JWT Token Validation');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.error('❌ No valid session found - please sign in first');
      return testResults;
    }
    
    console.log('✅ Valid session found:', {
      userId: session.user.id,
      email: session.user.email,
      tokenType: session.token_type,
      expiresAt: new Date(session.expires_at * 1000).toISOString()
    });
    
    // Verify token structure
    const token = session.access_token;
    const tokenParts = token.split('.');
    
    if (tokenParts.length === 3) {
      console.log('✅ JWT token has correct structure (header.payload.signature)');
      
      // Decode payload (without verification - just for inspection)
      try {
        const payload = JSON.parse(atob(tokenParts[1]));
        console.log('✅ JWT payload decoded:', {
          sub: payload.sub,
          aud: payload.aud,
          role: payload.role,
          iat: new Date(payload.iat * 1000).toISOString(),
          exp: new Date(payload.exp * 1000).toISOString()
        });
        
        // Check if token is not expired
        const now = Math.floor(Date.now() / 1000);
        if (payload.exp > now) {
          console.log('✅ JWT token is not expired');
          testResults.tokenExpiry = true;
        } else {
          console.error('❌ JWT token is expired');
        }
        
      } catch (decodeError) {
        console.error('❌ Failed to decode JWT payload:', decodeError);
      }
    } else {
      console.error('❌ Invalid JWT token structure');
    }
    
    testResults.jwtValidation = true;
    
  } catch (error) {
    console.error('❌ JWT validation test failed:', error);
    return testResults;
  }

  // Test 2: User ID Consistency
  console.log('\n👤 TEST 2: User ID Consistency');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get user from session
    const { data: { session } } = await supabase.auth.getSession();
    const sessionUserId = session.user.id;
    
    // Get user from getUser() call
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ Failed to get user:', userError);
      return testResults;
    }
    
    const getUserId = user.id;
    
    // Check consistency
    if (sessionUserId === getUserId) {
      console.log('✅ User ID consistent across auth methods:', sessionUserId);
      
      // Test database query with this user ID
      const { data: moodboards, error: queryError } = await supabase
        .from('moodboards')
        .select('user_id')
        .eq('user_id', sessionUserId)
        .limit(5);
      
      if (queryError) {
        console.log('✅ Database query with user ID works (or no data):', queryError.message);
      } else {
        const allMatchUserId = moodboards.every(mb => mb.user_id === sessionUserId);
        if (allMatchUserId) {
          console.log('✅ All returned records match authenticated user ID');
        } else {
          console.error('❌ Some records do not match authenticated user ID');
          return testResults;
        }
      }
      
      testResults.userIdConsistency = true;
      
    } else {
      console.error('❌ User ID inconsistency detected:', { sessionUserId, getUserId });
      return testResults;
    }
    
  } catch (error) {
    console.error('❌ User ID consistency test failed:', error);
    return testResults;
  }

  // Test 3: Session Security
  console.log('\n🔒 TEST 3: Session Security');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Check if session is properly secured
    const { data: { session } } = await supabase.auth.getSession();
    
    // Verify session has required security properties
    const securityChecks = {
      hasAccessToken: !!session.access_token,
      hasRefreshToken: !!session.refresh_token,
      hasExpiryTime: !!session.expires_at,
      hasUserMetadata: !!session.user,
      tokenTypeBearer: session.token_type === 'bearer'
    };
    
    console.log('✅ Session security checks:', securityChecks);
    
    const allSecurityChecksPassed = Object.values(securityChecks).every(check => check === true);
    if (allSecurityChecksPassed) {
      console.log('✅ All session security checks passed');
      testResults.sessionSecurity = true;
    } else {
      console.error('❌ Some session security checks failed');
    }
    
  } catch (error) {
    console.error('❌ Session security test failed:', error);
    return testResults;
  }

  // Test 4: Backend Authentication
  console.log('\n🖥️ TEST 4: Backend Authentication');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { session } } = await supabase.auth.getSession();
    
    // Test backend API call with authentication
    const response = await fetch('http://localhost:8000/api/moodboard/stats/summary', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend authentication successful:', {
        status: response.status,
        hasData: !!data,
        userSpecific: true // Backend should only return user-specific data
      });
      testResults.backendAuth = true;
    } else {
      console.error('❌ Backend authentication failed:', {
        status: response.status,
        statusText: response.statusText
      });
    }
    
  } catch (error) {
    console.error('❌ Backend authentication test failed:', error);
    return testResults;
  }

  // Test 5: Authorization Boundary Testing
  console.log('\n🚧 TEST 5: Authorization Boundary Testing');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test 5a: Try to access without authentication header
    try {
      const noAuthResponse = await fetch('http://localhost:8000/api/moodboard/list', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
          // No Authorization header
        }
      });
      
      if (noAuthResponse.status === 401 || noAuthResponse.status === 403) {
        console.log('✅ Backend properly rejects requests without authentication');
      } else {
        console.warn('⚠️ Backend allows requests without authentication (may use fallback)');
      }
    } catch (noAuthError) {
      console.log('✅ Backend properly rejects unauthenticated requests');
    }
    
    // Test 5b: Try with malformed token
    try {
      const badTokenResponse = await fetch('http://localhost:8000/api/moodboard/list', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer invalid-token-12345',
          'Content-Type': 'application/json'
        }
      });
      
      if (badTokenResponse.status === 401 || badTokenResponse.status === 403) {
        console.log('✅ Backend properly rejects malformed tokens');
      } else {
        console.warn('⚠️ Backend may not properly validate token format');
      }
    } catch (badTokenError) {
      console.log('✅ Backend properly rejects malformed tokens');
    }
    
  } catch (error) {
    console.error('❌ Authorization boundary test failed:', error);
  }

  // Calculate overall authentication score
  const totalTests = Object.keys(testResults).length - 1; // Exclude overallAuthScore
  const passedTests = Object.values(testResults).filter(result => result === true).length;
  testResults.overallAuthScore = Math.round((passedTests / totalTests) * 100);

  // Final Results
  console.log('\n📊 AUTHENTICATION & AUTHORIZATION TEST RESULTS');
  console.log('='.repeat(60));
  
  Object.entries(testResults).forEach(([test, result]) => {
    if (test !== 'overallAuthScore') {
      const status = result ? '✅ SECURE' : '❌ VULNERABLE';
      const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`${status} ${testName}`);
    }
  });
  
  console.log('\n🎯 OVERALL AUTHENTICATION SCORE:', `${testResults.overallAuthScore}%`);
  
  if (testResults.overallAuthScore === 100) {
    console.log('\n🎉 EXCELLENT! All authentication tests passed!');
  } else if (testResults.overallAuthScore >= 80) {
    console.log('\n⚠️ GOOD - Most authentication measures working');
  } else {
    console.log('\n🚨 CRITICAL - Authentication vulnerabilities detected!');
  }
  
  return testResults;
}

// Run the authentication flow validation
validateAuthFlow().catch(console.error);
