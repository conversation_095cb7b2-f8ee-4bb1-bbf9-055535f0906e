# Mood Board Image Storage Strategy

## Overview

The mood board implementation uses a different but optimal image storage strategy compared to the Visual Complexity Analyzer, tailored specifically for the interactive nature of Tldraw-based mood boards.

## Storage Strategy Comparison

### Visual Complexity Analyzer Approach
- **Use Case**: Single image analysis with results storage
- **Storage**: Separate Supabase Storage bucket (`design-analysis-images`)
- **Data Flow**: Upload → Store in bucket → Save file path in database
- **Retrieval**: Download from bucket using stored path

### Mood Board Approach (Current Implementation)
- **Use Case**: Interactive canvas with multiple images, shapes, and content
- **Storage**: Embedded in Tldraw snapshot as JSONB
- **Data Flow**: Add to canvas → Embed in snapshot → Save complete state
- **Retrieval**: Load snapshot → Tldraw reconstructs complete state

## Technical Implementation

### How Tldraw Handles Images

1. **Image Addition**: When users add images to the mood board:
   ```javascript
   // User drags/drops or uploads image
   // Tldraw automatically processes it
   ```

2. **State Capture**: Images are embedded in the snapshot:
   ```javascript
   const snapshot = editor.getSnapshot()
   // snapshot contains:
   // - All shapes and their properties
   // - Images as base64 data URLs or external URLs
   // - Positions, transformations, styling
   // - Layer order and grouping
   ```

3. **Database Storage**: Complete state stored as JSONB:
   ```sql
   UPDATE moodboards 
   SET tldraw_data = $1 
   WHERE id = $2 AND user_id = $3
   ```

4. **State Restoration**: Load and reconstruct:
   ```javascript
   editor.loadSnapshot(moodboard.tldraw_data)
   // Tldraw automatically reconstructs:
   // - All shapes and images
   // - Exact positions and styling
   // - Complete interactive state
   ```

## Advantages of Current Approach

### ✅ Simplicity
- No separate file management needed
- Single source of truth (JSONB snapshot)
- Atomic saves (all data in one transaction)

### ✅ Consistency
- Images always match the mood board state
- No orphaned files or broken references
- Version history includes complete state

### ✅ Performance
- Fast loading (single database query)
- No additional HTTP requests for images
- Efficient JSONB querying and indexing

### ✅ User Experience
- Instant image embedding
- No upload progress bars needed
- Seamless copy/paste from clipboard

## Data Structure Example

```json
{
  "store": {
    "asset:123": {
      "id": "asset:123",
      "typeName": "asset",
      "type": "image",
      "props": {
        "name": "image.jpg",
        "src": "data:image/jpeg;base64,/9j/4AAQSkZJRgABA...",
        "w": 800,
        "h": 600,
        "mimeType": "image/jpeg",
        "isAnimated": false
      }
    },
    "shape:456": {
      "id": "shape:456",
      "typeName": "shape",
      "type": "image",
      "props": {
        "assetId": "asset:123",
        "w": 400,
        "h": 300
      },
      "x": 100,
      "y": 50
    }
  },
  "schema": { /* schema definition */ }
}
```

## User Isolation

### Database Level (RLS Policies)
```sql
-- Users can only access their own moodboards
CREATE POLICY "Users can view their own moodboards" ON moodboards
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own moodboards" ON moodboards
    FOR UPDATE USING (auth.uid()::text = user_id);
```

### Application Level
```typescript
// Authentication check before save
if (!user) {
  toast({
    title: "Error",
    description: "Debes iniciar sesión para guardar",
    variant: "destructive"
  })
  return
}

// User ID automatically included in all operations
const result = await createMoodboard({
  title: tempTitle,
  tldraw_data: snapshot, // Contains all images
  // ... other properties
})
```

## Performance Considerations

### Storage Efficiency
- **Base64 Overhead**: ~33% size increase for embedded images
- **JSONB Compression**: PostgreSQL automatically compresses JSONB data
- **Practical Limit**: Reasonable for typical mood board usage

### Query Performance
- **Single Query**: Load complete mood board state
- **JSONB Indexing**: Efficient queries on mood board properties
- **Caching**: Browser caches reconstructed images

## Alternative Approaches Considered

### Hybrid Approach (Not Implemented)
- Store large images in Supabase Storage
- Keep small images embedded in snapshot
- **Complexity**: Would require size thresholds and fallback logic

### Full External Storage (Not Implemented)
- Store all images in Supabase Storage
- Reference by URL in Tldraw snapshot
- **Issues**: More complex, potential for broken references

## Conclusion

The current embedded storage approach is optimal for mood boards because:

1. **Tldraw is designed for it**: Native support for embedded images
2. **Atomic consistency**: Complete state always preserved together
3. **Simplicity**: No additional storage management needed
4. **Performance**: Single query loads complete interactive state
5. **User Experience**: Seamless image handling

This approach follows the principle of using the right tool for the job - while Visual Complexity Analyzer needs separate image storage for analysis workflows, mood boards benefit from Tldraw's built-in state management capabilities.
