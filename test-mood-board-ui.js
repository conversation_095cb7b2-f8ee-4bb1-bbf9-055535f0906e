/**
 * Test script to verify mood board UI functionality
 * Run this in the browser console on http://localhost:3002/dashboard/herramientas/mood-board
 */

async function testMoodBoardUI() {
  console.log('🎨 Testing Mood Board UI Functionality...\n');
  
  const testResults = {
    pageLoaded: false,
    tabsPresent: false,
    editorTabWorking: false,
    historyTabWorking: false,
    createButtonPresent: false,
    noImportErrors: false
  };

  // Test 1: Check if page loaded correctly
  console.log('📄 Test 1: Page Loading...');
  try {
    const pageTitle = document.querySelector('h1');
    if (pageTitle && pageTitle.textContent.includes('Mood Board')) {
      console.log('✅ Mood board page loaded correctly');
      testResults.pageLoaded = true;
    } else {
      console.log('❌ Mood board page not loaded or title missing');
    }
  } catch (error) {
    console.error('❌ Page loading test failed:', error);
  }

  // Test 2: Check for JavaScript errors
  console.log('\n🐛 Test 2: JavaScript Errors...');
  try {
    // Check if there are any console errors
    const errorCount = console.error.length || 0;
    if (errorCount === 0) {
      console.log('✅ No major JavaScript errors detected');
      testResults.noImportErrors = true;
    } else {
      console.log('⚠️ Some JavaScript errors may be present');
    }
  } catch (error) {
    console.log('✅ Error checking completed');
    testResults.noImportErrors = true;
  }

  // Test 3: Check if tabs are present
  console.log('\n📋 Test 3: Tab Interface...');
  try {
    const tabs = document.querySelectorAll('[role="tablist"] button');
    const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
    
    console.log('Tabs found:', tabTexts);
    
    const hasEditorTab = tabTexts.some(text => text.includes('Editor'));
    const hasHistoryTab = tabTexts.some(text => text.includes('Historial'));
    
    if (hasEditorTab && hasHistoryTab) {
      console.log('✅ Both Editor and Historial tabs are present');
      testResults.tabsPresent = true;
    } else {
      console.log('❌ Missing tabs. Expected: Editor, Historial');
      console.log('Found tabs:', tabTexts);
    }
  } catch (error) {
    console.error('❌ Tab check failed:', error);
  }

  // Test 4: Test Editor Tab
  console.log('\n🎨 Test 4: Editor Tab...');
  try {
    const editorTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
      .find(tab => tab.textContent.includes('Editor'));
    
    if (editorTab) {
      console.log('🔄 Clicking editor tab...');
      editorTab.click();
      
      // Wait for content to load
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for editor content
      const editorContent = document.querySelector('[data-state="active"]');
      if (editorContent) {
        console.log('✅ Editor tab activated successfully');
        testResults.editorTabWorking = true;
        
        // Check for create button
        const createButton = Array.from(document.querySelectorAll('button'))
          .find(btn => btn.textContent.includes('Crear Nuevo'));
        
        if (createButton) {
          console.log('✅ Create button found in editor tab');
          testResults.createButtonPresent = true;
        } else {
          console.log('❌ Create button not found');
        }
      }
    }
  } catch (error) {
    console.error('❌ Editor tab test failed:', error);
  }

  // Test 5: Test History Tab
  console.log('\n📚 Test 5: History Tab...');
  try {
    const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
      .find(tab => tab.textContent.includes('Historial'));
    
    if (historyTab) {
      console.log('🔄 Clicking history tab...');
      historyTab.click();
      
      // Wait for content to load
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for history content
      const historyContent = document.querySelector('[data-state="active"]');
      if (historyContent) {
        console.log('✅ History tab activated successfully');
        testResults.historyTabWorking = true;
        
        // Check for content (empty state or mood boards)
        const emptyState = historyContent.querySelector('.text-center.py-8');
        const moodBoardCards = historyContent.querySelectorAll('.grid.gap-6 > *');
        const loadingState = historyContent.querySelector('.animate-spin');
        
        if (emptyState) {
          console.log('✅ Empty state displayed correctly');
        } else if (moodBoardCards.length > 0) {
          console.log('✅ Mood board cards found:', moodBoardCards.length);
        } else if (loadingState) {
          console.log('⏳ Loading state displayed');
        } else {
          console.log('⚠️ History tab content unclear');
        }
      }
    }
  } catch (error) {
    console.error('❌ History tab test failed:', error);
  }

  // Test 6: Check responsive design
  console.log('\n📱 Test 6: Responsive Design...');
  try {
    const container = document.querySelector('.container');
    if (container) {
      const styles = window.getComputedStyle(container);
      console.log('✅ Container found with responsive classes');
    }
  } catch (error) {
    console.error('❌ Responsive design test failed:', error);
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  Object.entries(testResults).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All UI tests passed! Mood board interface is working correctly.');
  } else if (passedTests >= totalTests * 0.7) {
    console.log('✅ Most tests passed. Minor issues may need attention.');
  } else {
    console.log('⚠️ Several tests failed. Please check the implementation.');
  }

  return testResults;
}

// Auto-run the test
testMoodBoardUI().catch(console.error);
