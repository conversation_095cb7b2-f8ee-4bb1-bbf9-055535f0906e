-- Visual Complexity Analyzer - Duplicate Analysis Cleanup Script
-- This script identifies and removes duplicate analysis entries while preserving the correct ones

-- Step 1: Identify duplicate groups
-- Find analyses with same user_id, original_filename, and file_size (representing same analysis session)
WITH duplicate_groups AS (
  SELECT 
    user_id,
    original_filename,
    file_size,
    COUNT(*) as duplicate_count,
    ARRAY_AGG(id ORDER BY created_at) as analysis_ids,
    ARRAY_AGG(file_url ORDER BY created_at) as file_urls,
    ARRAY_AGG(created_at ORDER BY created_at) as timestamps,
    ARRAY_AGG(overall_score ORDER BY created_at) as scores
  FROM design_analyses
  GROUP BY user_id, original_filename, file_size
  HAVING COUNT(*) > 1
),

-- Step 2: Determine which entries to keep vs delete
cleanup_plan AS (
  SELECT 
    user_id,
    original_filename,
    file_size,
    duplicate_count,
    analysis_ids,
    file_urls,
    timestamps,
    -- Keep the entry with valid file_url (has image), or the first one if all have images
    CASE 
      WHEN CARDINALITY(ARRAY_REMOVE(file_urls, NULL)) > 0 THEN
        -- At least one entry has an image - keep the first one with image
        analysis_ids[ARRAY_POSITION(file_urls, (ARRAY_REMOVE(file_urls, NULL))[1])]
      ELSE
        -- No entries have images - keep the first one (oldest)
        analysis_ids[1]
    END as id_to_keep,
    
    -- Mark entries to delete (all except the one to keep)
    ARRAY_REMOVE(
      analysis_ids,
      CASE 
        WHEN CARDINALITY(ARRAY_REMOVE(file_urls, NULL)) > 0 THEN
          analysis_ids[ARRAY_POSITION(file_urls, (ARRAY_REMOVE(file_urls, NULL))[1])]
        ELSE
          analysis_ids[1]
      END
    ) as ids_to_delete
  FROM duplicate_groups
)

-- Step 3: Display cleanup plan for review
SELECT 
  user_id,
  original_filename,
  file_size,
  duplicate_count,
  id_to_keep,
  ids_to_delete,
  CARDINALITY(ids_to_delete) as entries_to_delete_count
FROM cleanup_plan
ORDER BY duplicate_count DESC, user_id;

-- Step 4: Execute cleanup (uncomment to run)
-- WARNING: This will permanently delete duplicate entries!

/*
-- Delete duplicate entries (keeping the correct ones)
WITH duplicate_groups AS (
  SELECT 
    user_id,
    original_filename,
    file_size,
    COUNT(*) as duplicate_count,
    ARRAY_AGG(id ORDER BY created_at) as analysis_ids,
    ARRAY_AGG(file_url ORDER BY created_at) as file_urls
  FROM design_analyses
  GROUP BY user_id, original_filename, file_size
  HAVING COUNT(*) > 1
),

cleanup_plan AS (
  SELECT 
    -- Keep the entry with valid file_url (has image), or the first one if all have images
    CASE 
      WHEN CARDINALITY(ARRAY_REMOVE(file_urls, NULL)) > 0 THEN
        -- At least one entry has an image - keep the first one with image
        analysis_ids[ARRAY_POSITION(file_urls, (ARRAY_REMOVE(file_urls, NULL))[1])]
      ELSE
        -- No entries have images - keep the first one (oldest)
        analysis_ids[1]
    END as id_to_keep,
    
    -- Mark entries to delete (all except the one to keep)
    ARRAY_REMOVE(
      analysis_ids,
      CASE 
        WHEN CARDINALITY(ARRAY_REMOVE(file_urls, NULL)) > 0 THEN
          analysis_ids[ARRAY_POSITION(file_urls, (ARRAY_REMOVE(file_urls, NULL))[1])]
        ELSE
          analysis_ids[1]
      END
    ) as ids_to_delete
  FROM duplicate_groups
),

ids_to_delete_unnested AS (
  SELECT UNNEST(ids_to_delete) as id_to_delete
  FROM cleanup_plan
  WHERE CARDINALITY(ids_to_delete) > 0
)

DELETE FROM design_analyses 
WHERE id IN (SELECT id_to_delete FROM ids_to_delete_unnested);

-- Show cleanup results
SELECT 
  'Cleanup completed' as status,
  COUNT(*) as total_remaining_analyses
FROM design_analyses;
*/
