// Test script to verify Color Palette Generator save functionality is fixed
// Run this in the browser console after navigating to the Color Palette Generator

console.log('🎨 Testing Color Palette Generator Save Fix...');

async function testPaletteSaveFix() {
  console.log('\n🔧 Step 1: Check Authentication...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Check if user is authenticated
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Auth error:', error);
      return false;
    }
    
    if (!session) {
      console.warn('⚠️ No active session - please log in first');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      userId: session.user.id,
      email: session.user.email,
      hasAccessToken: !!session.access_token
    });
    
    console.log('\n🔧 Step 2: Test API Connection...');
    
    // Test API connection with authentication
    const { api } = await import('/src/lib/api.ts');
    
    try {
      const response = await api.get('/api/palettes?limit=1');
      console.log('✅ API connection successful:', {
        status: response.status,
        hasData: !!response.data,
        dataStructure: response.data ? Object.keys(response.data) : 'No data'
      });
    } catch (apiError) {
      console.error('❌ API connection failed:', {
        message: apiError.message,
        status: apiError.response?.status,
        data: apiError.response?.data
      });
      return false;
    }
    
    console.log('\n🔧 Step 3: Test Palette Creation...');
    
    // Test creating a palette
    const testPaletteData = {
      name: `Test Palette ${Date.now()}`,
      colors: ['#FF5733', '#33FF57', '#3357FF', '#FF33F5', '#F5FF33'],
      description: 'Test palette created by automated script',
      tags: ['test', 'automated'],
      is_favorite: false
    };
    
    console.log('Test palette data:', testPaletteData);
    
    try {
      const createResponse = await api.post('/api/palettes', testPaletteData);
      console.log('✅ Palette creation successful:', {
        status: createResponse.status,
        success: createResponse.data?.success,
        paletteId: createResponse.data?.palette?.id,
        paletteName: createResponse.data?.palette?.name
      });
      
      // Test retrieving the created palette
      console.log('\n🔧 Step 4: Test Palette Retrieval...');
      
      const listResponse = await api.get('/api/palettes?limit=5');
      console.log('✅ Palette retrieval successful:', {
        status: listResponse.status,
        palettesCount: listResponse.data?.palettes?.length || 0,
        totalCount: listResponse.data?.count || 0
      });
      
      // Show the created palette
      const createdPalette = listResponse.data?.palettes?.find(p => p.name === testPaletteData.name);
      if (createdPalette) {
        console.log('✅ Created palette found in list:', {
          id: createdPalette.id,
          name: createdPalette.name,
          colors: createdPalette.colors,
          created_at: createdPalette.created_at
        });
      }
      
      return true;
    } catch (createError) {
      console.error('❌ Palette creation failed:', {
        message: createError.message,
        status: createError.response?.status,
        data: createError.response?.data
      });
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function testFrontendIntegration() {
  console.log('\n🔧 Step 5: Test Frontend Integration...');
  
  try {
    // Check if usePalettes hook is available
    const { usePalettes } = await import('/src/hooks/use-palettes.ts');
    console.log('✅ usePalettes hook imported successfully');
    
    // Check validation functions
    const { validatePaletteData, validateHexColor, normalizeHexColor } = await import('/src/hooks/use-palettes.ts');
    
    // Test validation functions
    const testColors = ['#FF5733', '#33FF57', '#3357FF'];
    const validationResults = testColors.map(color => ({
      original: color,
      isValid: validateHexColor(color),
      normalized: normalizeHexColor(color)
    }));
    
    console.log('✅ Validation functions working:', validationResults);
    
    // Test palette data validation
    const testData = {
      name: 'Test Validation',
      colors: testColors,
      description: 'Test description',
      tags: ['test'],
      is_favorite: false
    };
    
    const errors = validatePaletteData(testData);
    console.log('✅ Palette validation result:', {
      hasErrors: errors.length > 0,
      errors: errors
    });
    
    return true;
  } catch (error) {
    console.error('❌ Frontend integration test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Color Palette Generator Save Fix Tests...\n');
  
  const backendTest = await testPaletteSaveFix();
  const frontendTest = await testFrontendIntegration();
  
  console.log('\n📊 Test Results Summary:');
  console.log(`${backendTest ? '✅' : '❌'} Backend API Integration: ${backendTest ? 'PASSED' : 'FAILED'}`);
  console.log(`${frontendTest ? '✅' : '❌'} Frontend Integration: ${frontendTest ? 'PASSED' : 'FAILED'}`);
  
  const allPassed = backendTest && frontendTest;
  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Color Palette Generator save functionality is now working!');
    console.log('You can now:');
    console.log('1. Create color palettes');
    console.log('2. Save them to the database');
    console.log('3. Load saved palettes');
    console.log('4. Update and delete palettes');
  } else {
    console.log('\n💡 Next Steps:');
    if (!backendTest) {
      console.log('- Check backend server logs for errors');
      console.log('- Verify Supabase database connection');
      console.log('- Ensure user is properly authenticated');
    }
    if (!frontendTest) {
      console.log('- Check frontend console for import errors');
      console.log('- Verify hook implementations');
    }
  }
  
  return { backendTest, frontendTest, allPassed };
}

// Auto-run tests
runAllTests().catch(error => {
  console.error('❌ Test execution failed:', error);
});
