-- Visual Complexity Analyzer - Database Constraints to Prevent Duplicates
-- This script adds database-level safeguards to prevent duplicate analysis entries

-- Step 1: Create a partial unique index to prevent duplicates within a time window
-- This prevents the same user from creating multiple analyses of the same file within 30 seconds

-- First, let's create a function to extract the time window (30-second intervals)
CREATE OR REPLACE FUNCTION get_time_window_30s(timestamp_val TIMESTAMPTZ)
RETURNS TIMESTAMPTZ AS $$
BEGIN
  -- Round down to 30-second intervals
  RETURN DATE_TRUNC('minute', timestamp_val) + 
         INTERVAL '30 seconds' * FLOOR(EXTRACT(SECOND FROM timestamp_val) / 30);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Step 2: Create a partial unique index to prevent duplicates
-- This allows the same file to be analyzed multiple times, but not within 30 seconds
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_design_analyses_no_duplicates_30s
ON design_analyses (user_id, original_filename, file_size, get_time_window_30s(created_at))
WHERE status = 'completed';

-- Step 3: Add a check constraint to ensure reasonable file sizes
-- This prevents potential abuse and helps with duplicate detection accuracy
ALTER TABLE design_analyses 
ADD CONSTRAINT IF NOT EXISTS chk_reasonable_file_size 
CHECK (file_size > 0 AND file_size < 100000000); -- Max 100MB

-- Step 4: Add a check constraint for filename length
-- This ensures filenames are reasonable and helps with indexing performance
ALTER TABLE design_analyses 
ADD CONSTRAINT IF NOT EXISTS chk_reasonable_filename_length 
CHECK (LENGTH(original_filename) > 0 AND LENGTH(original_filename) <= 255);

-- Step 5: Create an index for efficient duplicate checking
-- This supports the application-level duplicate detection
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_design_analyses_duplicate_check
ON design_analyses (user_id, original_filename, file_size, created_at DESC)
WHERE status = 'completed';

-- Step 6: Add a trigger function to log potential duplicate attempts
-- This helps with monitoring and debugging
CREATE OR REPLACE FUNCTION log_duplicate_analysis_attempt()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if there's a recent analysis with same characteristics
  IF EXISTS (
    SELECT 1 FROM design_analyses 
    WHERE user_id = NEW.user_id 
      AND original_filename = NEW.original_filename 
      AND file_size = NEW.file_size 
      AND created_at > NEW.created_at - INTERVAL '30 seconds'
      AND id != NEW.id
      AND status = 'completed'
  ) THEN
    -- Log the potential duplicate (you can customize this logging)
    RAISE NOTICE 'Potential duplicate analysis detected for user % file % size %', 
                 NEW.user_id, NEW.original_filename, NEW.file_size;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_log_duplicate_analysis ON design_analyses;
CREATE TRIGGER trg_log_duplicate_analysis
  AFTER INSERT ON design_analyses
  FOR EACH ROW
  EXECUTE FUNCTION log_duplicate_analysis_attempt();

-- Step 7: Create a view for monitoring duplicate patterns
CREATE OR REPLACE VIEW v_duplicate_analysis_monitoring AS
WITH potential_duplicates AS (
  SELECT 
    user_id,
    original_filename,
    file_size,
    DATE_TRUNC('minute', created_at) as time_window,
    COUNT(*) as analysis_count,
    ARRAY_AGG(id ORDER BY created_at) as analysis_ids,
    ARRAY_AGG(file_url ORDER BY created_at) as file_urls,
    ARRAY_AGG(created_at ORDER BY created_at) as timestamps,
    MIN(created_at) as first_analysis,
    MAX(created_at) as last_analysis,
    MAX(created_at) - MIN(created_at) as time_span
  FROM design_analyses
  WHERE status = 'completed'
    AND created_at > NOW() - INTERVAL '24 hours'
  GROUP BY user_id, original_filename, file_size, DATE_TRUNC('minute', created_at)
  HAVING COUNT(*) > 1
)
SELECT 
  user_id,
  original_filename,
  file_size,
  time_window,
  analysis_count,
  analysis_ids,
  file_urls,
  timestamps,
  first_analysis,
  last_analysis,
  EXTRACT(EPOCH FROM time_span) as time_span_seconds,
  CASE 
    WHEN time_span < INTERVAL '30 seconds' THEN 'LIKELY_DUPLICATE'
    WHEN time_span < INTERVAL '2 minutes' THEN 'POSSIBLE_DUPLICATE'
    ELSE 'NORMAL_PATTERN'
  END as duplicate_likelihood,
  CARDINALITY(ARRAY_REMOVE(file_urls, NULL)) as entries_with_images
FROM potential_duplicates
ORDER BY time_span ASC, analysis_count DESC;

-- Step 8: Create a function to clean up duplicates (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_duplicate_analyses(
  dry_run BOOLEAN DEFAULT TRUE,
  time_window_hours INTEGER DEFAULT 24
)
RETURNS TABLE(
  action TEXT,
  user_id TEXT,
  original_filename TEXT,
  file_size INTEGER,
  duplicate_count BIGINT,
  kept_analysis_id UUID,
  deleted_analysis_ids UUID[]
) AS $$
DECLARE
  cleanup_record RECORD;
  deleted_ids UUID[];
BEGIN
  -- Find and process duplicate groups
  FOR cleanup_record IN
    WITH duplicate_groups AS (
      SELECT 
        da.user_id,
        da.original_filename,
        da.file_size,
        COUNT(*) as duplicate_count,
        ARRAY_AGG(da.id ORDER BY da.created_at) as analysis_ids,
        ARRAY_AGG(da.file_url ORDER BY da.created_at) as file_urls
      FROM design_analyses da
      WHERE da.status = 'completed'
        AND da.created_at > NOW() - (time_window_hours || ' hours')::INTERVAL
      GROUP BY da.user_id, da.original_filename, da.file_size
      HAVING COUNT(*) > 1
    )
    SELECT 
      dg.user_id,
      dg.original_filename,
      dg.file_size,
      dg.duplicate_count,
      -- Keep the entry with valid file_url, or the first one if all have images
      CASE 
        WHEN CARDINALITY(ARRAY_REMOVE(dg.file_urls, NULL)) > 0 THEN
          dg.analysis_ids[ARRAY_POSITION(dg.file_urls, (ARRAY_REMOVE(dg.file_urls, NULL))[1])]
        ELSE
          dg.analysis_ids[1]
      END as id_to_keep,
      -- Mark entries to delete
      ARRAY_REMOVE(
        dg.analysis_ids,
        CASE 
          WHEN CARDINALITY(ARRAY_REMOVE(dg.file_urls, NULL)) > 0 THEN
            dg.analysis_ids[ARRAY_POSITION(dg.file_urls, (ARRAY_REMOVE(dg.file_urls, NULL))[1])]
          ELSE
            dg.analysis_ids[1]
        END
      ) as ids_to_delete
    FROM duplicate_groups dg
  LOOP
    deleted_ids := cleanup_record.ids_to_delete;
    
    IF NOT dry_run AND CARDINALITY(deleted_ids) > 0 THEN
      -- Actually delete the duplicates
      DELETE FROM design_analyses WHERE id = ANY(deleted_ids);
    END IF;
    
    -- Return the action taken
    RETURN QUERY SELECT 
      CASE WHEN dry_run THEN 'DRY_RUN' ELSE 'DELETED' END::TEXT,
      cleanup_record.user_id,
      cleanup_record.original_filename,
      cleanup_record.file_size,
      cleanup_record.duplicate_count,
      cleanup_record.id_to_keep,
      deleted_ids;
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Usage examples:
-- 
-- -- Preview what would be cleaned up (dry run)
-- SELECT * FROM cleanup_duplicate_analyses(dry_run := TRUE);
-- 
-- -- Actually clean up duplicates from last 24 hours
-- SELECT * FROM cleanup_duplicate_analyses(dry_run := FALSE, time_window_hours := 24);
-- 
-- -- Monitor for duplicate patterns
-- SELECT * FROM v_duplicate_analysis_monitoring;

-- Step 9: Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT ON v_duplicate_analysis_monitoring TO your_app_user;
-- GRANT EXECUTE ON FUNCTION cleanup_duplicate_analyses TO your_admin_user;
