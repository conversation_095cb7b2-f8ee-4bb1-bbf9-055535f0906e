/**
 * Test script to verify Tldraw Spanish localization
 * Run this in the browser console on the mood board editor page
 */

async function testTldrawSpanishLocalization() {
  console.log('🌍 Testing Tldraw Spanish Localization...\n');
  
  // Test 1: Check if Tldraw component is loaded
  console.log('📡 Step 1: Checking Tldraw Component...');
  
  const tldrawContainer = document.querySelector('.tl-container');
  if (!tldrawContainer) {
    console.error('❌ Tldraw editor not found. Make sure you are on the mood board editor page.');
    return false;
  }
  
  console.log('✅ Tldraw editor found');
  
  // Test 2: Check for Spanish UI elements
  console.log('\n🔍 Step 2: Checking Spanish UI Elements...');
  
  const spanishElements = [
    // Common Spanish words that should appear in the UI
    'Seleccionar',
    'Dibujar',
    'Texto',
    'Rectángulo',
    'Círculo',
    'Flecha',
    'Línea',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>ar',
    'Eliminar',
    'Duplicar',
    'Agrupar',
    'Desagrupar',
    'Zoom',
    'Ajustar',
    'Exportar',
    'Imprimir'
  ];
  
  let foundSpanishElements = 0;
  let totalChecked = 0;
  
  spanishElements.forEach(word => {
    // Check if the word appears in any visible text
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      return el.textContent && el.textContent.includes(word) && 
             el.offsetParent !== null; // Only visible elements
    });
    
    if (elements.length > 0) {
      console.log(`✅ Found Spanish text: "${word}"`);
      foundSpanishElements++;
    }
    totalChecked++;
  });
  
  console.log(`\n📊 Spanish localization check: ${foundSpanishElements}/${totalChecked} elements found`);
  
  // Test 3: Check for English elements (should be minimal)
  console.log('\n🔍 Step 3: Checking for English Elements...');
  
  const englishElements = [
    'Select',
    'Draw',
    'Text',
    'Rectangle',
    'Circle',
    'Arrow',
    'Line',
    'Eraser',
    'Hand',
    'Note',
    'Frame',
    'Image',
    'Undo',
    'Redo',
    'Copy',
    'Paste',
    'Delete',
    'Duplicate',
    'Group',
    'Ungroup',
    'Zoom',
    'Fit',
    'Export',
    'Print'
  ];
  
  let foundEnglishElements = 0;
  
  englishElements.forEach(word => {
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      return el.textContent && el.textContent.includes(word) && 
             el.offsetParent !== null;
    });
    
    if (elements.length > 0) {
      console.log(`⚠️ Found English text: "${word}"`);
      foundEnglishElements++;
    }
  });
  
  console.log(`\n📊 English elements found: ${foundEnglishElements}/${englishElements.length}`);
  
  // Test 4: Check toolbar tooltips
  console.log('\n🔍 Step 4: Checking Toolbar Tooltips...');
  
  const toolbarButtons = document.querySelectorAll('.tl-toolbar button, .tl-toolbar [role="button"]');
  console.log(`Found ${toolbarButtons.length} toolbar buttons`);
  
  let tooltipTests = 0;
  let spanishTooltips = 0;
  
  toolbarButtons.forEach((button, index) => {
    if (index < 5) { // Test first 5 buttons to avoid overwhelming
      const title = button.getAttribute('title') || button.getAttribute('aria-label');
      if (title) {
        tooltipTests++;
        console.log(`Button ${index + 1} tooltip: "${title}"`);
        
        // Check if tooltip contains Spanish words
        const hasSpanishWords = spanishElements.some(word => title.includes(word));
        if (hasSpanishWords) {
          spanishTooltips++;
        }
      }
    }
  });
  
  console.log(`\n📊 Tooltip check: ${spanishTooltips}/${tooltipTests} tooltips in Spanish`);
  
  // Test 5: Check context menu
  console.log('\n🔍 Step 5: Testing Context Menu...');
  
  try {
    // Simulate right-click on canvas to open context menu
    const canvas = document.querySelector('.tl-canvas');
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const contextMenuEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
        clientX: centerX,
        clientY: centerY,
        button: 2
      });
      
      canvas.dispatchEvent(contextMenuEvent);
      
      // Wait a bit for context menu to appear
      setTimeout(() => {
        const contextMenu = document.querySelector('.tl-context-menu, [role="menu"]');
        if (contextMenu) {
          console.log('✅ Context menu opened');
          
          const menuItems = contextMenu.querySelectorAll('[role="menuitem"], .tl-menu-item');
          let spanishMenuItems = 0;
          
          menuItems.forEach(item => {
            const text = item.textContent;
            if (text) {
              const hasSpanishWords = spanishElements.some(word => text.includes(word));
              if (hasSpanishWords) {
                spanishMenuItems++;
                console.log(`✅ Spanish menu item: "${text}"`);
              }
            }
          });
          
          console.log(`📊 Context menu: ${spanishMenuItems}/${menuItems.length} items in Spanish`);
          
          // Close context menu
          document.addEventListener('click', () => {}, { once: true });
          document.body.click();
        } else {
          console.log('⚠️ Context menu not found or not opened');
        }
      }, 500);
    }
  } catch (error) {
    console.log('⚠️ Could not test context menu:', error.message);
  }
  
  // Final Assessment
  console.log('\n🎯 FINAL ASSESSMENT');
  console.log('='.repeat(50));
  
  const spanishPercentage = Math.round((foundSpanishElements / totalChecked) * 100);
  const englishPercentage = Math.round((foundEnglishElements / englishElements.length) * 100);
  
  console.log(`Spanish elements found: ${spanishPercentage}%`);
  console.log(`English elements remaining: ${englishPercentage}%`);
  
  if (spanishPercentage >= 70) {
    console.log('\n🎉 EXCELLENT! Spanish localization is working well!');
    console.log('✅ Tldraw is properly configured for Spanish language');
  } else if (spanishPercentage >= 40) {
    console.log('\n⚠️ PARTIAL - Some Spanish localization detected');
    console.log('🔧 May need additional configuration or translation updates');
  } else {
    console.log('\n❌ POOR - Spanish localization not working properly');
    console.log('🚨 Check TLUiTranslationProvider configuration');
  }
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  if (foundSpanishElements === 0) {
    console.log('1. Verify TLUiTranslationProvider is properly imported');
    console.log('2. Check if locale="es" is correctly set');
    console.log('3. Ensure Tldraw version supports Spanish translations');
  } else if (spanishPercentage < 70) {
    console.log('1. Some UI elements may still be in English');
    console.log('2. Consider adding custom translation overrides');
    console.log('3. Check if all Tldraw components are wrapped in translation provider');
  } else {
    console.log('1. Spanish localization is working correctly!');
    console.log('2. Monitor for any missing translations in new features');
  }
  
  return {
    spanishPercentage,
    englishPercentage,
    foundSpanishElements,
    foundEnglishElements,
    success: spanishPercentage >= 40
  };
}

// Run the test
testTldrawSpanishLocalization().catch(console.error);
