/**
 * Simple test script to test mood board creation
 * Run this in the browser console on the mood board editor page
 */

async function testMoodBoardCreation() {
  console.log('🧪 Testing Mood Board Creation');
  console.log('==============================');

  try {
    // Step 1: Get authentication
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.error('❌ Authentication failed:', authError);
      return;
    }
    
    console.log('✅ User authenticated:', user.id);

    // Step 2: Create test mood board
    const testData = {
      title: "Test Mood Board " + new Date().toISOString(),
      description: "Test description",
      tldraw_data: {
        records: [],
        bindings: [],
        assets: []
      },
      tags: ["test"],
      is_public: false,
      is_favorite: false,
      collaboration_enabled: false,
      shared_with: [],
      notes: "Test notes"
    };

    console.log('📤 Creating mood board...');
    
    const response = await fetch('http://localhost:8000/api/moodboard/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(testData)
    });

    console.log('📡 Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ Success!', result);
    
    if (result.success && result.data?.id) {
      console.log('🎉 Mood board created with ID:', result.data.id);
      
      // Refresh the page to see the new mood board
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testMoodBoardCreation();
