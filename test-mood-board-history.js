/**
 * Test script to verify mood board history functionality
 * Run this in the browser console on http://localhost:3002/dashboard/herramientas/mood-board
 */

async function testMoodBoardHistory() {
  console.log('🎨 Testing Mood Board History Functionality...\n');
  
  const testResults = {
    authentication: false,
    tabsPresent: false,
    historyTabWorking: false,
    emptyStateCorrect: false,
    createMoodBoard: false,
    historyDisplay: false
  };

  // Test 1: Check Authentication
  console.log('🔐 Test 1: Authentication...');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ User not authenticated. Please sign in first.');
      console.log('💡 You can sign in at: http://localhost:3002/login');
      return testResults;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      sessionValid: !!session
    });
    testResults.authentication = true;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return testResults;
  }

  // Test 2: Check if tabs are present
  console.log('\n📋 Test 2: Checking tabs...');
  try {
    const tabs = document.querySelectorAll('[role="tablist"] button');
    const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
    
    console.log('Tabs found:', tabTexts);
    
    const hasEditorTab = tabTexts.some(text => text.includes('Editor'));
    const hasHistoryTab = tabTexts.some(text => text.includes('Historial'));
    
    if (hasEditorTab && hasHistoryTab) {
      console.log('✅ Both Editor and Historial tabs are present');
      testResults.tabsPresent = true;
    } else {
      console.log('❌ Missing tabs. Expected: Editor, Historial');
    }
  } catch (error) {
    console.error('❌ Tab check failed:', error);
  }

  // Test 3: Test History Tab
  console.log('\n📚 Test 3: Testing History Tab...');
  try {
    const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
      .find(tab => tab.textContent.includes('Historial'));
    
    if (historyTab) {
      console.log('🔄 Clicking history tab...');
      historyTab.click();
      
      // Wait for content to load
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for history content
      const historyContent = document.querySelector('[data-state="active"]');
      if (historyContent) {
        console.log('✅ History tab activated successfully');
        testResults.historyTabWorking = true;
        
        // Check for empty state or mood boards
        const emptyState = historyContent.querySelector('.text-center.py-8');
        const moodBoardCards = historyContent.querySelectorAll('.grid.gap-6 > *');
        
        if (emptyState) {
          console.log('✅ Empty state displayed correctly');
          testResults.emptyStateCorrect = true;
        } else if (moodBoardCards.length > 0) {
          console.log('✅ Mood board cards found:', moodBoardCards.length);
          testResults.historyDisplay = true;
        }
      }
    }
  } catch (error) {
    console.error('❌ History tab test failed:', error);
  }

  // Test 4: Test Editor Tab and Create Button
  console.log('\n🎨 Test 4: Testing Editor Tab...');
  try {
    const editorTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
      .find(tab => tab.textContent.includes('Editor'));
    
    if (editorTab) {
      console.log('🔄 Clicking editor tab...');
      editorTab.click();
      
      // Wait for content to load
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for create button
      const createButton = document.querySelector('button:has-text("Crear Nuevo Mood Board")') ||
                          Array.from(document.querySelectorAll('button'))
                            .find(btn => btn.textContent.includes('Crear Nuevo'));
      
      if (createButton) {
        console.log('✅ Create button found in editor tab');
        testResults.createMoodBoard = true;
      }
    }
  } catch (error) {
    console.error('❌ Editor tab test failed:', error);
  }

  // Test 5: Check Database Connection
  console.log('\n🗄️ Test 5: Database Connection...');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data, error } = await supabase
      .from('moodboards')
      .select('id, title, created_at')
      .limit(5);
    
    if (error) {
      console.error('❌ Database query failed:', error);
    } else {
      console.log('✅ Database connection successful. Mood boards found:', data.length);
    }
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  Object.entries(testResults).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Mood board history functionality is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }

  return testResults;
}

// Auto-run the test
testMoodBoardHistory().catch(console.error);
