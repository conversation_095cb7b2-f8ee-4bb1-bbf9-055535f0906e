// Test script to verify Mood Board Editor is working
console.log('🎨 Testing Mood Board Editor Fix');

function testMoodBoardEditor() {
  console.log('\n📋 Test 1: Page Loading');
  
  const currentUrl = window.location.href;
  console.log('Current URL:', currentUrl);
  
  if (currentUrl.includes('mood-board/editor')) {
    console.log('✅ Correct URL path');
  } else {
    console.log('❌ Incorrect URL path');
    return false;
  }
  
  // Check for JavaScript errors
  console.log('\n📋 Test 2: JavaScript Errors');
  
  // Look for error messages in console
  const originalError = console.error;
  let errorCount = 0;
  
  console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.error = originalError;
    if (errorCount === 0) {
      console.log('✅ No JavaScript errors detected');
    } else {
      console.log(`❌ ${errorCount} JavaScript errors detected`);
    }
  }, 2000);
  
  // Check for main components
  console.log('\n📋 Test 3: Component Loading');
  
  setTimeout(() => {
    // Check for Tldraw canvas
    const tldrawCanvas = document.querySelector('canvas, [data-testid="canvas"]');
    if (tldrawCanvas) {
      console.log('✅ Tldraw canvas found');
    } else {
      console.log('❌ Tldraw canvas not found');
    }
    
    // Check for toolbar
    const toolbar = document.querySelector('[class*="toolbar"], .toolbar, button');
    if (toolbar) {
      console.log('✅ Toolbar elements found');
    } else {
      console.log('❌ Toolbar elements not found');
    }
    
    // Check for title input
    const titleInput = document.querySelector('input[type="text"], input[placeholder*="título"], input[placeholder*="title"]');
    if (titleInput) {
      console.log('✅ Title input found');
    } else {
      console.log('❌ Title input not found');
    }
    
    // Check for back button
    const backButton = document.querySelector('button[class*="back"], button:has(svg)');
    if (backButton) {
      console.log('✅ Back button found');
    } else {
      console.log('❌ Back button not found');
    }
    
  }, 3000);
  
  return true;
}

function testAuthLoading() {
  console.log('\n📋 Test 4: Auth Loading State');
  
  // Check if there's a loading spinner
  const loadingSpinner = document.querySelector('[class*="animate-spin"], .loader, [class*="loading"]');
  if (loadingSpinner) {
    console.log('⚠️ Loading spinner detected - waiting for auth...');
    
    // Wait for loading to complete
    setTimeout(() => {
      const stillLoading = document.querySelector('[class*="animate-spin"], .loader, [class*="loading"]');
      if (!stillLoading) {
        console.log('✅ Loading completed');
        testMoodBoardEditor();
      } else {
        console.log('❌ Still loading after 5 seconds');
      }
    }, 5000);
  } else {
    console.log('✅ No loading state detected');
    testMoodBoardEditor();
  }
}

function testImagePlaceholders() {
  console.log('\n📋 Test 5: Image Placeholder Issues');
  
  // Check for failed image loads
  const images = document.querySelectorAll('img');
  let failedImages = 0;
  
  images.forEach((img, index) => {
    if (img.src.includes('via.placeholder.com')) {
      failedImages++;
      console.log(`❌ Image ${index + 1}: Still using via.placeholder.com - ${img.src.substring(0, 50)}...`);
    } else if (img.src.startsWith('data:image/svg+xml')) {
      console.log(`✅ Image ${index + 1}: Using local SVG placeholder`);
    } else {
      console.log(`ℹ️ Image ${index + 1}: ${img.src.substring(0, 50)}...`);
    }
  });
  
  if (failedImages === 0) {
    console.log('✅ No problematic placeholder images found');
  } else {
    console.log(`❌ Found ${failedImages} images still using via.placeholder.com`);
  }
}

// Run tests
console.log('🚀 Starting Mood Board Editor tests...');
testAuthLoading();
testImagePlaceholders();

// Test network connectivity
console.log('\n📋 Test 6: Network Connectivity');
fetch('/api/v1/health')
  .then(response => {
    if (response.ok) {
      console.log('✅ Backend API is reachable');
    } else {
      console.log('❌ Backend API returned error:', response.status);
    }
  })
  .catch(error => {
    console.log('❌ Backend API is not reachable:', error.message);
  });
