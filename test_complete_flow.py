#!/usr/bin/env python3
"""
Complete end-to-end test for Visual Complexity Analyzer image storage fix
Tests the entire flow: image upload → analysis → storage → database save → retrieval
"""

import asyncio
import requests
import json
from io import BytesIO
from PIL import Image
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_complete_flow():
    """Test the complete Visual Complexity Analyzer flow"""
    
    print("🧪 COMPLETE END-TO-END TEST: Visual Complexity Analyzer")
    print("=" * 70)
    
    try:
        # Step 1: Create a realistic test image
        print("\n📸 Step 1: Creating realistic test image...")
        img = Image.new('RGB', (800, 600), color='white')
        
        # Draw a complex design with multiple elements
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        
        # Background gradient effect
        for y in range(600):
            color_value = int(255 * (y / 600))
            draw.line([(0, y), (800, y)], fill=(255 - color_value, 200, color_value))
        
        # Add geometric shapes
        draw.rectangle([50, 50, 200, 150], fill='red', outline='black', width=3)
        draw.ellipse([250, 50, 400, 200], fill='blue', outline='white', width=2)
        draw.polygon([(450, 50), (550, 50), (500, 150)], fill='green', outline='black')
        
        # Add text elements
        try:
            # Try to use a default font
            font = ImageFont.load_default()
            draw.text((100, 300), "VISUAL COMPLEXITY TEST", fill='black', font=font)
            draw.text((100, 350), "Multiple Elements & Colors", fill='darkblue', font=font)
            draw.text((100, 400), "Testing Image Storage Fix", fill='darkred', font=font)
        except:
            # Fallback if font loading fails
            draw.text((100, 300), "VISUAL COMPLEXITY TEST", fill='black')
            draw.text((100, 350), "Multiple Elements & Colors", fill='darkblue')
            draw.text((100, 400), "Testing Image Storage Fix", fill='darkred')
        
        # Convert to bytes
        img_buffer = BytesIO()
        img.save(img_buffer, format='PNG', quality=95)
        img_buffer.seek(0)
        img_bytes = img_buffer.getvalue()
        
        print(f"✅ Test image created: {len(img_bytes)} bytes, 800x600 PNG")
        
        # Step 2: Test the backend analysis endpoint
        print("\n🔬 Step 2: Testing backend analysis endpoint...")
        
        files = {
            'design': ('visual_complexity_test.png', img_bytes, 'image/png')
        }
        
        # Note: For a real test, you'd need a valid JWT token
        # For now, testing without authentication (should still work with updated RLS policy)
        headers = {
            'Accept': 'application/json'
        }
        
        print("📤 Sending analysis request to backend...")
        response = requests.post(
            'http://localhost:5001/api/design-analysis/analyze-design',  # ✅ FIXED: Correct endpoint
            files=files,
            headers=headers,
            timeout=60  # Longer timeout for analysis
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Backend analysis successful!")
            print(f"📊 Analysis Results:")
            print(f"   - Success: {result.get('success', 'NOT SET')}")
            print(f"   - Analysis ID: {result.get('analysis_id', 'NOT SET')}")
            print(f"   - Saved to Database: {result.get('saved_to_database', 'NOT SET')}")
            print(f"   - Score: {result.get('score', 'NOT SET')}")
            print(f"   - Visual Complexity: {result.get('visual_complexity', 'NOT SET')}")
            print(f"   - Cognitive Load: {result.get('cognitive_load', 'NOT SET')}")
            
            analysis_id = result.get('analysis_id')
            if not analysis_id:
                print("❌ No analysis_id returned - cannot verify database record")
                return False
                
            # Step 3: Verify database record
            print(f"\n💾 Step 3: Verifying database record for analysis: {analysis_id}")
            
            # Import Supabase service to check database
            from app.core.supabase import SupabaseService
            service = SupabaseService()
            
            # Query the database directly
            try:
                result_db = service.client.table('design_analyses').select('*').eq('id', analysis_id).execute()
                
                if result_db.data and len(result_db.data) > 0:
                    record = result_db.data[0]
                    print("✅ Database record found!")
                    print(f"📊 Database Record:")
                    print(f"   - ID: {record.get('id')}")
                    print(f"   - File URL: {record.get('file_url', 'NULL')}")
                    print(f"   - Original Filename: {record.get('original_filename')}")
                    print(f"   - File Size: {record.get('file_size')}")
                    print(f"   - File Type: {record.get('file_type')}")
                    print(f"   - Score: {record.get('score')}")
                    
                    file_url = record.get('file_url')
                    if file_url:
                        print("🎉 SUCCESS: file_url is populated in database!")
                        
                        # Step 4: Verify file exists in storage
                        print(f"\n🗄️ Step 4: Verifying file exists in storage: {file_url}")
                        
                        storage_result = service.client.table('objects').select('*').eq('name', file_url).eq('bucket_id', 'design-analysis-images').execute()
                        
                        if storage_result.data and len(storage_result.data) > 0:
                            storage_record = storage_result.data[0]
                            print("✅ File exists in storage!")
                            print(f"📊 Storage Record:")
                            print(f"   - Name: {storage_record.get('name')}")
                            print(f"   - Bucket: {storage_record.get('bucket_id')}")
                            print(f"   - Size: {storage_record.get('metadata', {}).get('size', 'Unknown')}")
                            print(f"   - Created: {storage_record.get('created_at')}")
                            
                            print("\n🎯 COMPLETE SUCCESS!")
                            print("✅ Image uploaded to storage")
                            print("✅ Analysis completed")
                            print("✅ Database record created with file_url")
                            print("✅ File exists in storage")
                            print("\n🔧 THE FIX IS WORKING PERFECTLY!")
                            
                            return True
                        else:
                            print("❌ File not found in storage")
                            return False
                    else:
                        print("❌ CRITICAL: file_url is still NULL in database")
                        return False
                else:
                    print("❌ Database record not found")
                    return False
                    
            except Exception as db_error:
                print(f"❌ Database verification failed: {str(db_error)}")
                return False
                
        else:
            print(f"❌ Backend analysis failed: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"📊 Error details: {error_detail}")
            except:
                print(f"📊 Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_complete_flow())
    if success:
        print("\n🎉 ALL TESTS PASSED - VISUAL COMPLEXITY ANALYZER FIX VERIFIED!")
        exit(0)
    else:
        print("\n❌ TESTS FAILED - ISSUE STILL EXISTS")
        exit(1)
