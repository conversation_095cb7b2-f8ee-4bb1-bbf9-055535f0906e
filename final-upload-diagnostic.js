/**
 * FINAL UPLOAD DIAGNOSTIC - IMMEDIATE EXECUTION
 * This script runs automatically and provides immediate results
 * 
 * Copy and paste this entire script into the browser console
 */

console.log('🚀 FINAL UPLOAD DIAGNOSTIC - STARTING IMMEDIATELY...');

// Auto-execute function
(async function finalDiagnostic() {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    errors: [],
    success: false,
    rootCause: null,
    solution: null
  };

  try {
    console.log('🔍 Step 1: Environment Check');
    
    // Check if we're on the right page
    if (!window.location.href.includes('design-complexity-analyzer')) {
      console.error('❌ Not on Visual Complexity Analyzer page');
      console.log('📍 Current URL:', window.location.href);
      console.log('💡 Navigate to: /tools/design-complexity-analyzer');
      return;
    }
    
    // Wait for services to be available
    let attempts = 0;
    while ((!window.supabase || !window.designAnalysisService) && attempts < 10) {
      console.log(`⏳ Waiting for services... (attempt ${attempts + 1}/10)`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }
    
    if (!window.supabase) {
      console.error('❌ Supabase not available after waiting');
      results.errors.push('Supabase not available');
      return results;
    }
    
    if (!window.designAnalysisService) {
      console.error('❌ designAnalysisService not available after waiting');
      results.errors.push('designAnalysisService not available');
      return results;
    }
    
    console.log('✅ Environment OK - Services available');
    results.tests.environment = { success: true };

    console.log('🔐 Step 2: Authentication Check');
    const { data: { user }, error: authError } = await window.supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      results.errors.push(`Authentication failed: ${authError?.message || 'No user'}`);
      results.rootCause = 'AUTHENTICATION_FAILURE';
      results.solution = 'Please log in to your account and try again';
      return results;
    }
    
    console.log('✅ Authenticated as:', user.email);
    results.tests.authentication = { success: true, user: user.email };

    console.log('🪣 Step 3: Storage Connection Test');
    try {
      const { data: buckets, error: bucketError } = await window.supabase.storage.listBuckets();
      
      if (bucketError) {
        throw new Error(`Bucket list error: ${bucketError.message}`);
      }
      
      const designBucket = buckets.find(b => b.name === 'design-analysis-images');
      if (!designBucket) {
        throw new Error('design-analysis-images bucket not found');
      }
      
      console.log('✅ Storage connection OK');
      results.tests.storageConnection = { success: true };
    } catch (storageError) {
      console.error('❌ Storage connection failed:', storageError);
      results.errors.push(`Storage connection failed: ${storageError.message}`);
      results.rootCause = 'STORAGE_CONNECTION_FAILURE';
      results.solution = 'Check Supabase Storage configuration and bucket permissions';
      return results;
    }

    console.log('📤 Step 4: Direct Upload Test');
    try {
      const testContent = `test-${Date.now()}`;
      const testBlob = new Blob([testContent], { type: 'text/plain' });
      const testFile = new File([testBlob], 'final-test.txt', { type: 'text/plain' });
      const fileName = `${user.id}/final-test-${Date.now()}.txt`;
      
      console.log(`📝 Uploading to: ${fileName}`);
      
      const { data, error } = await window.supabase.storage
        .from('design-analysis-images')
        .upload(fileName, testFile, {
          cacheControl: '3600',
          upsert: false
        });
      
      if (error) {
        console.error('❌ Direct upload failed:', error);
        console.error('Error details:', {
          message: error.message,
          code: error.code,
          status: error.status
        });
        
        if (error.message.includes('row-level security policy')) {
          results.rootCause = 'RLS_POLICY_VIOLATION';
          results.solution = 'RLS policies are still blocking uploads - the fix did not work completely';
        } else if (error.message.includes('permission') || error.message.includes('Policy')) {
          results.rootCause = 'PERMISSION_ERROR';
          results.solution = 'Check authentication and RLS policies for storage.objects';
        } else if (error.message.includes('Duplicate')) {
          results.rootCause = 'DUPLICATE_FILE_ERROR';
          results.solution = 'File naming collision - try with different timestamp';
        } else {
          results.rootCause = 'UNKNOWN_UPLOAD_ERROR';
          results.solution = `Unknown upload error: ${error.message}`;
        }
        
        results.errors.push(`Direct upload failed: ${error.message}`);
        results.tests.directUpload = { success: false, error: error.message };
        return results;
      }
      
      console.log('✅ Direct upload successful:', data.path);
      results.tests.directUpload = { success: true, path: data.path };
      
      // Clean up
      await window.supabase.storage
        .from('design-analysis-images')
        .remove([fileName]);
      
    } catch (uploadError) {
      console.error('❌ Direct upload exception:', uploadError);
      results.errors.push(`Direct upload exception: ${uploadError.message}`);
      results.rootCause = 'UPLOAD_EXCEPTION';
      results.solution = `Upload threw exception: ${uploadError.message}`;
      return results;
    }

    console.log('🔧 Step 5: Service Upload Test');
    try {
      // Create test image
      const canvas = document.createElement('canvas');
      canvas.width = 5;
      canvas.height = 5;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 5, 5);
      
      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
      const imageFile = new File([blob], 'service-test.png', { type: 'image/png' });
      
      console.log(`📷 Created test image: ${imageFile.name} (${imageFile.size} bytes)`);
      
      const serviceResult = await window.designAnalysisService.uploadImage(imageFile, user.id);
      
      if (!serviceResult) {
        console.error('❌ Service upload returned null');
        results.errors.push('Service upload returned null');
        results.rootCause = 'SERVICE_UPLOAD_NULL';
        results.solution = 'Service uploadImage method is returning null - check implementation';
        results.tests.serviceUpload = { success: false, error: 'Returned null' };
        return results;
      }
      
      console.log('✅ Service upload successful:', serviceResult);
      results.tests.serviceUpload = { success: true, path: serviceResult };
      
      // Clean up
      await window.supabase.storage
        .from('design-analysis-images')
        .remove([serviceResult]);
      
    } catch (serviceError) {
      console.error('❌ Service upload failed:', serviceError);
      results.errors.push(`Service upload failed: ${serviceError.message}`);
      results.rootCause = 'SERVICE_UPLOAD_ERROR';
      results.solution = `Service upload error: ${serviceError.message}`;
      results.tests.serviceUpload = { success: false, error: serviceError.message };
      return results;
    }

    console.log('💾 Step 6: Complete Flow Test');
    try {
      // Create test image
      const canvas = document.createElement('canvas');
      canvas.width = 10;
      canvas.height = 10;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#00FF00';
      ctx.fillRect(0, 0, 10, 10);
      
      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
      const imageFile = new File([blob], 'complete-test.png', { type: 'image/png' });
      
      const testData = {
        user_id: user.id,
        tool_type: 'visual-complexity',
        original_filename: imageFile.name,
        file_size: imageFile.size,
        file_type: imageFile.type,
        overall_score: 85,
        complexity_scores: { color: 8, layout: 8, typography: 8, elements: 9 },
        analysis_areas: [{ name: 'Test', score: 8, description: 'Final diagnostic test' }],
        recommendations: [{ category: 'Test', issue: 'Test', importance: 'alta', recommendation: 'Test' }],
        ai_analysis_summary: 'Final diagnostic test analysis',
        agent_message: 'Testing complete flow',
        visuai_insights: 'Test insights',
        tags: ['final-diagnostic-test']
      };
      
      console.log('💾 Calling saveAnalysis...');
      
      const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, imageFile);
      
      console.log(`✅ Analysis saved: ${savedAnalysis.id}`);
      console.log(`📁 File URL: ${savedAnalysis.file_url || 'NULL'}`);
      
      if (!savedAnalysis.file_url) {
        console.error('❌ Complete flow failed - file_url is null');
        results.errors.push('Complete flow: file_url is null in saved analysis');
        results.rootCause = 'FILE_URL_NULL';
        results.solution = 'saveAnalysis is not populating file_url - check upload integration in saveAnalysis method';
        results.tests.completeFlow = { success: false, error: 'file_url is null' };
        
        // Clean up
        await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
        return results;
      }
      
      console.log('✅ Complete flow successful - file_url populated!');
      results.tests.completeFlow = { success: true, analysisId: savedAnalysis.id, fileUrl: savedAnalysis.file_url };
      
      // Clean up
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('🧹 Test analysis cleaned up');
      
    } catch (completeError) {
      console.error('❌ Complete flow failed:', completeError);
      results.errors.push(`Complete flow failed: ${completeError.message}`);
      results.rootCause = 'COMPLETE_FLOW_ERROR';
      results.solution = `Complete flow error: ${completeError.message}`;
      results.tests.completeFlow = { success: false, error: completeError.message };
      return results;
    }

    // If we get here, everything worked!
    results.success = true;
    results.rootCause = 'NO_ISSUES_FOUND';
    results.solution = 'All tests passed - upload functionality is working correctly';

    console.log('🎉 ALL TESTS PASSED!');
    console.log('✅ Upload functionality is working correctly');
    
    return results;

  } catch (error) {
    console.error('💥 Diagnostic failed:', error);
    results.errors.push(`Diagnostic failed: ${error.message}`);
    results.rootCause = 'DIAGNOSTIC_FAILURE';
    results.solution = `Diagnostic script failed: ${error.message}`;
    return results;
    
  } finally {
    // Generate final report
    console.log('\n📊 FINAL DIAGNOSTIC REPORT');
    console.log('============================');
    console.log(`🕐 Timestamp: ${results.timestamp}`);
    console.log(`🎯 Success: ${results.success ? '✅ YES' : '❌ NO'}`);
    console.log(`🔍 Root Cause: ${results.rootCause || 'Unknown'}`);
    console.log(`💡 Solution: ${results.solution || 'No solution provided'}`);
    
    if (results.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    console.log('\n📋 TEST RESULTS:');
    Object.entries(results.tests).forEach(([test, result]) => {
      console.log(`  ${test}: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
      if (!result.success && result.error) {
        console.log(`    Error: ${result.error}`);
      }
    });
    
    // Store results globally
    window.finalDiagnosticResults = results;
    console.log('\n💾 Results stored in window.finalDiagnosticResults');
    
    if (results.success) {
      console.log('\n🎯 CONCLUSION: Upload functionality is working correctly!');
      console.log('💡 Try uploading an image in the Visual Complexity Analyzer now.');
    } else {
      console.log('\n🎯 CONCLUSION: Upload functionality has issues.');
      console.log(`💡 NEXT STEP: ${results.solution}`);
    }
  }
})();

console.log('📋 Final diagnostic script executed - results will appear above');
