// Test script for Color Palette Generator fixes
// Run this in the browser console to test the complete flow

console.log('🎨 Color Palette Generator - Fix Verification Script');

async function testColorExtractionFix() {
  console.log('\n🔧 Test 1: Color Extraction Fix...');
  
  // Test the fixed hex color conversion
  const testRGBValues = [
    [255, 87, 51],   // #FF5733
    [51, 255, 87],   // #33FF57
    [51, 87, 255],   // #3357FF
    [255, 255, 255], // #FFFFFF
    [0, 0, 0],       // #000000
    [128, 128, 128], // #808080
  ];
  
  console.log('Testing RGB to Hex conversion...');
  
  testRGBValues.forEach(([r, g, b]) => {
    // Test the old problematic method
    const oldMethod = `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, "0")}`;
    
    // Test the new fixed method
    const newMethod = `#${(((r << 16) | (g << 8) | b) >>> 0).toString(16).padStart(6, "0")}`;
    
    console.log(`RGB(${r}, ${g}, ${b}):`, {
      oldMethod,
      newMethod,
      isValid: /^#[0-9A-F]{6}$/i.test(newMethod)
    });
  });
  
  return true;
}

async function testAuthenticationSetup() {
  console.log('\n🔐 Test 2: Authentication Setup...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Check if user is authenticated
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Auth error:', error);
      return false;
    }
    
    if (!session) {
      console.warn('⚠️ No active session - user needs to log in');
      return false;
    }
    
    console.log('✅ User authenticated:', {
      userId: session.user.id,
      email: session.user.email,
      hasAccessToken: !!session.access_token
    });
    
    return true;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return false;
  }
}

async function testAPIConnection() {
  console.log('\n📡 Test 3: API Connection...');
  
  try {
    const { api } = await import('/src/lib/api.ts');
    
    // Test a simple API call
    const response = await api.get('/api/palettes?limit=1');
    
    console.log('✅ API connection successful:', {
      status: response.status,
      hasData: !!response.data,
      dataStructure: response.data ? Object.keys(response.data) : 'No data'
    });
    
    return true;
  } catch (error) {
    console.error('❌ API connection failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return false;
  }
}

async function testPaletteValidation() {
  console.log('\n✅ Test 4: Palette Validation...');
  
  try {
    const { validatePaletteData, validateHexColor, normalizeHexColor } = await import('/src/hooks/use-palettes.ts');
    
    // Test hex color validation
    const testColors = [
      '#FF5733',   // Valid 6-digit
      '#F53',      // Valid 3-digit
      'FF5733',    // Valid without #
      '#GGGGGG',   // Invalid characters
      '#FF573',    // Invalid length
      '',          // Empty
    ];
    
    console.log('Testing hex color validation...');
    testColors.forEach(color => {
      const isValid = validateHexColor(color);
      const normalized = normalizeHexColor(color);
      console.log(`"${color}": valid=${isValid}, normalized="${normalized}"`);
    });
    
    // Test palette data validation
    const testPaletteData = {
      name: 'Test Palette',
      colors: ['#FF5733', '#33FF57', '#3357FF'],
      description: 'Test description',
      tags: [],
      is_favorite: false
    };
    
    const errors = validatePaletteData(testPaletteData);
    console.log('Palette validation errors:', errors);
    
    return errors.length === 0;
  } catch (error) {
    console.error('❌ Validation test failed:', error);
    return false;
  }
}

async function testCompleteSaveFlow() {
  console.log('\n💾 Test 5: Complete Save Flow...');
  
  try {
    const { usePalettes } = await import('/src/hooks/use-palettes.ts');
    
    // Note: This test requires the component to be mounted
    // We'll just verify the hook can be imported
    console.log('✅ usePalettes hook imported successfully');
    
    // Test data structure
    const testPaletteData = {
      name: `Test Palette ${Date.now()}`,
      colors: ['#FF5733', '#33FF57', '#3357FF', '#FF33F5', '#F5FF33'],
      description: 'Automatically generated test palette',
      tags: ['test', 'automated'],
      is_favorite: false
    };
    
    console.log('Test palette data structure:', testPaletteData);
    
    return true;
  } catch (error) {
    console.error('❌ Save flow test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Color Palette Generator Fix Tests...\n');
  
  const results = {
    colorExtraction: await testColorExtractionFix(),
    authentication: await testAuthenticationSetup(),
    apiConnection: await testAPIConnection(),
    validation: await testPaletteValidation(),
    saveFlow: await testCompleteSaveFlow()
  };
  
  console.log('\n📊 Test Results Summary:');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (!allPassed) {
    console.log('\n💡 Next Steps:');
    if (!results.authentication) {
      console.log('- Please log in to your account');
    }
    if (!results.apiConnection) {
      console.log('- Check backend server is running on port 8000');
      console.log('- Verify API endpoints are accessible');
    }
    if (!results.validation) {
      console.log('- Check validation functions implementation');
    }
  } else {
    console.log('\n🎉 All fixes are working correctly!');
    console.log('You can now:');
    console.log('1. Upload an image to extract colors');
    console.log('2. Generate color palettes');
    console.log('3. Save palettes to the database');
  }
  
  return results;
}

// Auto-run tests
runAllTests().catch(error => {
  console.error('❌ Test execution failed:', error);
});
