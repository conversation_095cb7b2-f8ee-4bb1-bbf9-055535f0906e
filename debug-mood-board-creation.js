/**
 * Debug script to test mood board creation and identify the 500 error
 */

async function debugMoodBoardCreation() {
  console.log('🔍 Debug: Mood Board Creation Issue');
  console.log('=====================================');

  try {
    // Step 1: Check authentication
    console.log('\n🔐 Step 1: Authentication Check...');
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.error('❌ Authentication failed:', authError);
      return;
    }
    
    console.log('✅ Authentication successful:', {
      userId: user.id,
      email: user.email,
      hasToken: !!session.access_token,
      tokenLength: session.access_token?.length
    });

    // Step 2: Test backend connectivity
    console.log('\n🔗 Step 2: Backend Connectivity...');
    try {
      const listResponse = await fetch('http://localhost:8000/api/moodboard/list?page=1&limit=1', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });
      
      console.log('📡 List endpoint response:', {
        status: listResponse.status,
        ok: listResponse.ok,
        statusText: listResponse.statusText
      });
      
      if (listResponse.ok) {
        const listData = await listResponse.json();
        console.log('✅ Backend connection working');
        console.log('📊 List response:', {
          success: listData.success,
          totalCount: listData.total_count
        });
      } else {
        const errorText = await listResponse.text();
        console.error('❌ List endpoint failed:', errorText);
      }
    } catch (error) {
      console.error('❌ Backend connectivity error:', error);
    }

    // Step 3: Test mood board creation with minimal data
    console.log('\n📝 Step 3: Mood Board Creation Test...');
    
    const testMoodBoard = {
      title: "Debug Test Mood Board",
      description: "Testing mood board creation",
      tldraw_data: {
        records: [],
        bindings: [],
        assets: []
      },
      tags: [],
      is_public: false,
      is_favorite: false,
      collaboration_enabled: false,
      shared_with: [],
      notes: "Debug test"
    };
    
    console.log('📤 Sending create request with data:', {
      title: testMoodBoard.title,
      hasDescription: !!testMoodBoard.description,
      hasTldrawData: !!testMoodBoard.tldraw_data,
      tagsCount: testMoodBoard.tags?.length || 0
    });

    const createResponse = await fetch('http://localhost:8000/api/moodboard/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(testMoodBoard)
    });
    
    console.log('📡 Create response status:', {
      status: createResponse.status,
      ok: createResponse.ok,
      statusText: createResponse.statusText,
      headers: Object.fromEntries(createResponse.headers.entries())
    });
    
    if (!createResponse.ok) {
      // Get detailed error information
      const errorText = await createResponse.text();
      console.error('❌ Create request failed');
      console.error('📄 Error response body:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        console.error('📋 Parsed error:', errorJson);
      } catch (parseError) {
        console.error('📋 Could not parse error as JSON');
      }
      
      return;
    }
    
    const createResult = await createResponse.json();
    console.log('✅ Create request successful!');
    console.log('📄 Create result:', {
      success: createResult.success,
      message: createResult.message,
      dataId: createResult.data?.id,
      dataTitle: createResult.data?.title
    });

    // Step 4: Verify the created mood board
    if (createResult.success && createResult.data?.id) {
      console.log('\n🔍 Step 4: Verification...');
      
      const verifyResponse = await fetch(`http://localhost:8000/api/moodboard/${createResult.data.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });
      
      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        console.log('✅ Mood board verified successfully');
        console.log('📊 Verified data:', {
          id: verifyData.data?.id,
          title: verifyData.data?.title,
          createdAt: verifyData.data?.created_at
        });
      } else {
        console.error('❌ Verification failed:', verifyResponse.status);
      }
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
    console.error('📋 Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
  }
}

// Run the debug script
debugMoodBoardCreation();
