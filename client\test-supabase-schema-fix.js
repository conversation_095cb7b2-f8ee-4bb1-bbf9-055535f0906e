// Test script to verify Supabase schema configuration fix
// Run this in the browser console to test the schema fix

console.log('🔧 Testing Supabase Schema Configuration Fix');

async function testSupabaseSchemaFix() {
  console.log('\n📡 Step 1: Testing Supabase Client Configuration...');
  
  try {
    // Import the updated Supabase client
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase client imported successfully');
    
    // Test authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) {
      console.error('❌ Authentication error:', authError);
      return false;
    }
    
    if (!user) {
      console.warn('⚠️ No authenticated user - please sign in first');
      return false;
    }
    
    console.log('✅ User authenticated:', { id: user.id, email: user.email });
    
    // Test basic query to design_analyses (should now use api schema)
    console.log('\n🔍 Step 2: Testing API Schema Access...');
    const { data, error } = await supabase
      .from('design_analyses')
      .select('id, user_id, original_filename, created_at')
      .eq('user_id', user.id)
      .limit(5);
    
    if (error) {
      console.error('❌ API Schema query failed:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      
      // Check if it's still a schema error
      if (error.message.includes('PGRST106') || error.message.includes('schema')) {
        console.error('🚨 Schema configuration still incorrect!');
        return false;
      }
      
      return false;
    }
    
    console.log('✅ API Schema query successful:', {
      recordsFound: data?.length || 0,
      userId: user.id,
      sampleRecord: data?.[0] || 'No records'
    });
    
    return { user, analyses: data || [] };
  } catch (error) {
    console.error('❌ Schema test failed:', error);
    return false;
  }
}

async function testDesignAnalysisService() {
  console.log('\n🔧 Step 3: Testing Design Analysis Service...');
  
  try {
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    
    // Get current user
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.warn('⚠️ No authenticated user for service test');
      return false;
    }
    
    // Test getUserAnalyses method
    console.log('Testing getUserAnalyses with new schema...');
    const analyses = await designAnalysisService.getUserAnalyses(user.id, {
      limit: 5,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('✅ getUserAnalyses successful:', {
      count: analyses.length,
      sample: analyses[0] ? {
        id: analyses[0].id,
        filename: analyses[0].original_filename,
        score: analyses[0].overall_score,
        created_at: analyses[0].created_at
      } : 'No analyses'
    });
    
    // Test getUserStats method
    console.log('Testing getUserStats with new schema...');
    const stats = await designAnalysisService.getUserStats(user.id);
    
    console.log('✅ getUserStats successful:', stats);
    
    return { analyses, stats };
  } catch (error) {
    console.error('❌ Design Analysis Service test failed:', error);
    return false;
  }
}

async function testImageStorage() {
  console.log('\n🖼️ Step 4: Testing Image Storage Integration...');
  
  try {
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.warn('⚠️ No authenticated user for image storage test');
      return false;
    }
    
    // Get analyses with file URLs
    const analyses = await designAnalysisService.getUserAnalyses(user.id, { limit: 3 });
    const analysesWithImages = analyses.filter(a => a.file_url);
    
    if (analysesWithImages.length === 0) {
      console.log('ℹ️ No analyses with images found for testing');
      return true;
    }
    
    console.log(`Found ${analysesWithImages.length} analyses with images`);
    
    // Test image URL generation for the first analysis with an image
    const testAnalysis = analysesWithImages[0];
    console.log('Testing image URL for analysis:', testAnalysis.id);
    
    const imageUrl = await designAnalysisService.getImageUrl(testAnalysis.file_url);
    
    if (imageUrl) {
      console.log('✅ Image URL generated successfully:', {
        analysisId: testAnalysis.id,
        urlType: imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL',
        urlPreview: imageUrl.substring(0, 50) + '...'
      });
      return true;
    } else {
      console.warn('⚠️ Image URL generation failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Image storage test failed:', error);
    return false;
  }
}

async function testHistoryTabFunctionality() {
  console.log('\n📋 Step 5: Testing History Tab Functionality...');
  
  try {
    // Check if we're on the Visual Complexity Analyzer page
    const historyTab = document.querySelector('[value="history"]');
    if (!historyTab) {
      console.warn('⚠️ History tab not found - make sure you\'re on the Visual Complexity Analyzer page');
      return false;
    }
    
    // Click the history tab
    console.log('Clicking history tab...');
    historyTab.click();
    
    // Wait for content to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for analysis cards or loading state
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], [class*="AnalysisCard"]');
    const loadingSpinner = document.querySelector('.animate-spin');
    const emptyState = document.querySelector('.text-center.py-8');
    
    if (loadingSpinner) {
      console.log('🔄 Still loading - waiting longer...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check again
      const finalCards = document.querySelectorAll('[class*="analysis-card"], [class*="AnalysisCard"]');
      const finalEmptyState = document.querySelector('.text-center.py-8');
      
      if (finalCards.length > 0) {
        console.log('✅ Analysis cards loaded after waiting:', finalCards.length);
        return true;
      } else if (finalEmptyState) {
        console.log('✅ Empty state displayed correctly');
        return true;
      } else {
        console.warn('⚠️ No content found after loading');
        return false;
      }
    } else if (analysisCards.length > 0) {
      console.log('✅ Analysis cards found immediately:', analysisCards.length);
      return true;
    } else if (emptyState) {
      console.log('✅ Empty state displayed correctly');
      return true;
    } else {
      console.warn('⚠️ No analysis cards, loading state, or empty state found');
      return false;
    }
  } catch (error) {
    console.error('❌ History tab test failed:', error);
    return false;
  }
}

async function runCompleteSchemaTest() {
  console.log('🚀 Running Complete Schema Fix Test...\n');
  
  const results = {
    schemaFix: false,
    serviceLayer: false,
    imageStorage: false,
    historyTab: false
  };
  
  // Test 1: Schema configuration fix
  results.schemaFix = await testSupabaseSchemaFix();
  
  if (results.schemaFix) {
    // Test 2: Service layer with new schema
    results.serviceLayer = await testDesignAnalysisService();
    
    // Test 3: Image storage integration
    results.imageStorage = await testImageStorage();
    
    // Test 4: History tab functionality
    results.historyTab = await testHistoryTabFunctionality();
  }
  
  console.log('\n📋 Schema Fix Test Results:');
  console.log('='.repeat(50));
  console.log('Schema Configuration:', results.schemaFix ? '✅ FIXED' : '❌ FAILED');
  console.log('Service Layer:', results.serviceLayer ? '✅ WORKING' : '❌ FAILED');
  console.log('Image Storage:', results.imageStorage ? '✅ WORKING' : '❌ FAILED');
  console.log('History Tab:', results.historyTab ? '✅ WORKING' : '❌ FAILED');
  
  if (results.schemaFix && results.serviceLayer) {
    console.log('\n🎉 SUCCESS: Schema configuration has been fixed!');
    console.log('✅ No more HTTP 406 errors should occur');
    console.log('✅ Visual Complexity Analyzer should work correctly');
    console.log('✅ Analysis saving and retrieval should function properly');
    
    if (results.imageStorage) {
      console.log('✅ Image storage and display should work correctly');
    }
    
    if (results.historyTab) {
      console.log('✅ History tab should display saved analyses');
    }
  } else {
    console.log('\n⚠️ Some issues remain:');
    if (!results.schemaFix) {
      console.log('- Schema configuration still needs fixing');
    }
    if (!results.serviceLayer) {
      console.log('- Service layer needs additional work');
    }
    if (!results.imageStorage) {
      console.log('- Image storage integration needs attention');
    }
    if (!results.historyTab) {
      console.log('- History tab functionality needs debugging');
    }
  }
  
  return results;
}

// Auto-run the complete test
runCompleteSchemaTest();

// Export functions for manual testing
window.testSupabaseSchemaFix = {
  runCompleteSchemaTest,
  testSupabaseSchemaFix,
  testDesignAnalysisService,
  testImageStorage,
  testHistoryTabFunctionality
};

console.log('\n📝 Available test functions:');
console.log('- window.testSupabaseSchemaFix.runCompleteSchemaTest()');
console.log('- window.testSupabaseSchemaFix.testSupabaseSchemaFix()');
console.log('- window.testSupabaseSchemaFix.testDesignAnalysisService()');
console.log('- window.testSupabaseSchemaFix.testImageStorage()');
console.log('- window.testSupabaseSchemaFix.testHistoryTabFunctionality()');
