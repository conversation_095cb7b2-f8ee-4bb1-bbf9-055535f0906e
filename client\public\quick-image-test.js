/**
 * Quick Image Display Test for Emma Studio
 * Run this in the browser console on the Visual Complexity Analyzer page
 * 
 * Usage: Copy and paste this entire script into the browser console and press Enter
 */

(async function quickImageDisplayTest() {
  console.log('🚀 Starting Quick Image Display Test...');
  
  try {
    // Step 1: Check if we're on the right page
    if (!window.location.pathname.includes('visual-complexity') && !window.location.pathname.includes('design-complexity')) {
      console.log('⚠️ Navigate to the Visual Complexity Analyzer page first');
      return;
    }
    
    // Step 2: Import required modules
    console.log('📦 Importing modules...');
    const { supabase } = await import('/src/lib/supabase.ts');
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    
    console.log('✅ Modules imported successfully');
    
    // Step 3: Check authentication
    console.log('🔐 Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ Authentication failed:', authError?.message || 'No user found');
      console.log('💡 Please log in and try again');
      return;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Step 4: Test storage access
    console.log('📦 Testing storage access...');
    const { data: files, error: listError } = await supabase.storage
      .from('design-analysis-images')
      .list(user.id, { limit: 3 });
    
    if (listError) {
      console.log('❌ Storage access failed:', listError.message);
      console.log('💡 Check RLS policies and bucket permissions');
      return;
    }
    
    console.log('✅ Storage accessible, found', files?.length || 0, 'files');
    
    if (!files || files.length === 0) {
      console.log('📝 No files found. Upload an image in the Visual Complexity Analyzer first.');
      return;
    }
    
    // Step 5: Test image retrieval
    console.log('🖼️ Testing image retrieval...');
    const testFile = files[0];
    const filePath = `${user.id}/${testFile.name}`;
    
    console.log('🧪 Testing file:', testFile.name);
    
    // Test direct download
    console.log('📥 Testing direct download...');
    const { data: fileBlob, error: downloadError } = await supabase.storage
      .from('design-analysis-images')
      .download(filePath);
    
    if (downloadError) {
      console.log('❌ Direct download failed:', downloadError.message);
      return;
    }
    
    console.log('✅ Direct download successful:', {
      size: fileBlob.size,
      type: fileBlob.type
    });
    
    // Test object URL creation
    console.log('🔗 Creating object URL...');
    const objectUrl = URL.createObjectURL(fileBlob);
    console.log('✅ Object URL created:', objectUrl.substring(0, 50) + '...');
    
    // Test image loading
    console.log('🧪 Testing image loading...');
    const loadTest = await new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        console.log('✅ Image loads successfully!');
        resolve(true);
      };
      img.onerror = (error) => {
        console.log('❌ Image failed to load:', error);
        resolve(false);
      };
      img.src = objectUrl;
      
      // Timeout after 5 seconds
      setTimeout(() => {
        console.log('⏰ Image load timeout');
        resolve(false);
      }, 5000);
    });
    
    // Clean up
    URL.revokeObjectURL(objectUrl);
    
    // Step 6: Test both service methods
    console.log('🔧 Testing primary service method (authenticated endpoint)...');
    const serviceUrl = await designAnalysisService.getImageUrl(filePath);
    let primaryServiceTest = false;

    if (serviceUrl) {
      console.log('✅ Primary service method successful:', {
        type: serviceUrl.startsWith('blob:') ? 'Blob URL' : 'HTTP URL',
        url: serviceUrl.substring(0, 50) + '...'
      });

      // Test service URL loading
      primaryServiceTest = await new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = serviceUrl;
        setTimeout(() => resolve(false), 5000);
      });

      console.log('🧪 Primary service URL test:', primaryServiceTest ? '✅ Success' : '❌ Failed');

      // Clean up service URL if it's a blob
      if (serviceUrl.startsWith('blob:')) {
        URL.revokeObjectURL(serviceUrl);
      }
    } else {
      console.log('❌ Primary service method failed');
    }

    // Step 7: Test fallback method
    console.log('🔧 Testing fallback service method (SDK download)...');
    const fallbackUrl = await designAnalysisService.getImageUrlFallback(filePath);
    let fallbackServiceTest = false;

    if (fallbackUrl) {
      console.log('✅ Fallback service method successful:', {
        type: fallbackUrl.startsWith('blob:') ? 'Blob URL' : 'HTTP URL',
        url: fallbackUrl.substring(0, 50) + '...'
      });

      // Test fallback URL loading
      fallbackServiceTest = await new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = fallbackUrl;
        setTimeout(() => resolve(false), 5000);
      });

      console.log('🧪 Fallback service URL test:', fallbackServiceTest ? '✅ Success' : '❌ Failed');

      // Clean up fallback URL if it's a blob
      if (fallbackUrl.startsWith('blob:')) {
        URL.revokeObjectURL(fallbackUrl);
      }
    } else {
      console.log('❌ Fallback service method failed');
    }
    
    // Step 8: Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('================');
    console.log('✅ Authentication: Working');
    console.log('✅ Storage Access: Working');
    console.log('✅ File Download: Working');
    console.log('✅ Object URL Creation: Working');
    console.log(loadTest ? '✅ Direct Image Loading: Working' : '❌ Direct Image Loading: Failed');
    console.log(primaryServiceTest ? '✅ Primary Service Method (Authenticated Endpoint): Working' : '❌ Primary Service Method: Failed');
    console.log(fallbackServiceTest ? '✅ Fallback Service Method (SDK Download): Working' : '❌ Fallback Service Method: Failed');

    const anyMethodWorking = loadTest || primaryServiceTest || fallbackServiceTest;
    const bestMethod = primaryServiceTest ? 'Authenticated Endpoint' : fallbackServiceTest ? 'SDK Download' : 'None';

    if (anyMethodWorking) {
      console.log(`\n🎉 SUCCESS! Image display is working using: ${bestMethod}`);
      console.log('💡 Recommended approach:', primaryServiceTest ? 'Primary method is working correctly' : 'Using fallback method');

      if (!primaryServiceTest && fallbackServiceTest) {
        console.log('⚠️ Note: Primary method failed but fallback works. This suggests an issue with the authenticated endpoint.');
      }

      console.log('\n💡 If you\'re still seeing issues in the app, try:');
      console.log('   1. Refresh the page');
      console.log('   2. Clear browser cache');
      console.log('   3. Check browser console for other errors');
    } else {
      console.log('\n❌ ALL METHODS FAILED. This indicates a serious issue with image display.');
      console.log('💡 Troubleshooting steps:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify you are logged in correctly');
      console.log('   3. Check RLS policies in Supabase dashboard');
      console.log('   4. Try uploading a new image');
      console.log('   5. Check browser console for detailed error messages');
    }
    
  } catch (error) {
    console.log('💥 Test failed with error:', error.message);
    console.log('🔍 Full error:', error);
  }
})();

console.log('🎯 Quick Image Display Test loaded and running...');
