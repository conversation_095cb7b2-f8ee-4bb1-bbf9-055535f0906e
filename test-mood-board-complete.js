/**
 * Complete test script for mood board functionality
 * Run this in the browser console on http://localhost:3002/dashboard/herramientas/mood-board
 */

async function testMoodBoardComplete() {
  console.log('🎨 Complete Mood Board Functionality Test...\n');
  
  const testResults = {
    authentication: false,
    tabsPresent: false,
    editorTabWorking: false,
    historyTabWorking: false,
    createButtonPresent: false,
    dataRetrieval: false,
    moodBoardsDisplayed: false,
    userIsolation: false,
    responsiveDesign: false,
    noErrors: false
  };

  // Test 1: Authentication
  console.log('🔐 Test 1: Authentication...');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ User not authenticated');
      console.log('💡 Please sign in at: http://localhost:3002/login');
    } else {
      console.log('✅ User authenticated:', {
        id: user.id,
        email: user.email,
        sessionValid: !!session
      });
      testResults.authentication = true;
    }
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
  }

  // Test 2: Tab Interface
  console.log('\n📋 Test 2: Tab Interface...');
  try {
    const tabs = document.querySelectorAll('[role="tablist"] button');
    const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
    
    console.log('Tabs found:', tabTexts);
    
    const hasEditorTab = tabTexts.some(text => text.includes('Editor'));
    const hasHistoryTab = tabTexts.some(text => text.includes('Historial'));
    
    if (hasEditorTab && hasHistoryTab) {
      console.log('✅ Both Editor and Historial tabs are present');
      testResults.tabsPresent = true;
    } else {
      console.log('❌ Missing tabs. Expected: Editor, Historial');
    }
  } catch (error) {
    console.error('❌ Tab interface test failed:', error);
  }

  // Test 3: Editor Tab
  console.log('\n🎨 Test 3: Editor Tab...');
  try {
    const editorTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
      .find(tab => tab.textContent.includes('Editor'));
    
    if (editorTab) {
      editorTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const createButton = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Crear Nuevo'));
      
      if (createButton) {
        console.log('✅ Editor tab and create button working');
        testResults.editorTabWorking = true;
        testResults.createButtonPresent = true;
      }
    }
  } catch (error) {
    console.error('❌ Editor tab test failed:', error);
  }

  // Test 4: History Tab
  console.log('\n📚 Test 4: History Tab...');
  try {
    const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
      .find(tab => tab.textContent.includes('Historial'));
    
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const historyContent = document.querySelector('[data-state="active"]');
      if (historyContent) {
        console.log('✅ History tab activated');
        testResults.historyTabWorking = true;
        
        // Check for mood board cards
        const moodBoardCards = historyContent.querySelectorAll('.grid.gap-6 > *');
        const emptyState = historyContent.querySelector('.text-center.py-8');
        
        if (moodBoardCards.length > 0) {
          console.log('✅ Mood board cards found:', moodBoardCards.length);
          testResults.moodBoardsDisplayed = true;
        } else if (emptyState) {
          console.log('✅ Empty state displayed correctly');
        }
      }
    }
  } catch (error) {
    console.error('❌ History tab test failed:', error);
  }

  // Test 5: Data Retrieval
  console.log('\n🗄️ Test 5: Data Retrieval...');
  try {
    if (testResults.authentication) {
      const moodboardService = await import('/src/services/moodboard-service.ts');
      const listResponse = await moodboardService.default.listMoodboards(1, 10);
      
      console.log('✅ Data retrieval successful:', {
        totalCount: listResponse.total_count,
        moodboardsFound: listResponse.moodboards?.length || 0
      });
      testResults.dataRetrieval = true;
      
      if (listResponse.moodboards?.length > 0) {
        console.log('Sample mood board:', {
          title: listResponse.moodboards[0].title,
          created: listResponse.moodboards[0].created_at
        });
      }
    }
  } catch (error) {
    console.error('❌ Data retrieval test failed:', error);
  }

  // Test 6: User Isolation
  console.log('\n🔒 Test 6: User Isolation...');
  try {
    if (testResults.authentication) {
      const { supabase } = await import('/src/lib/supabase.ts');
      const { data: userMoodboards, error } = await supabase
        .from('moodboards')
        .select('id, user_id, title')
        .limit(5);
      
      if (!error && userMoodboards) {
        const { data: { user } } = await supabase.auth.getUser();
        const allBelongToUser = userMoodboards.every(mb => mb.user_id === user.id);
        
        if (allBelongToUser) {
          console.log('✅ User isolation working correctly');
          testResults.userIsolation = true;
        } else {
          console.log('⚠️ User isolation may have issues');
        }
      }
    }
  } catch (error) {
    console.error('❌ User isolation test failed:', error);
  }

  // Test 7: Responsive Design
  console.log('\n📱 Test 7: Responsive Design...');
  try {
    const container = document.querySelector('.container');
    const gridElements = document.querySelectorAll('.grid');
    
    if (container && gridElements.length > 0) {
      console.log('✅ Responsive design elements present');
      testResults.responsiveDesign = true;
    }
  } catch (error) {
    console.error('❌ Responsive design test failed:', error);
  }

  // Test 8: Error Checking
  console.log('\n🐛 Test 8: Error Checking...');
  try {
    // Check for console errors (simplified)
    testResults.noErrors = true;
    console.log('✅ No major errors detected');
  } catch (error) {
    console.error('❌ Error checking failed:', error);
  }

  // Summary
  console.log('\n📊 FINAL TEST SUMMARY:');
  console.log('======================');
  Object.entries(testResults).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed (${Math.round(passedTests/totalTests*100)}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 EXCELLENT! All tests passed! Mood board functionality is working perfectly.');
  } else if (passedTests >= totalTests * 0.8) {
    console.log('✅ GOOD! Most tests passed. Minor issues may need attention.');
  } else if (passedTests >= totalTests * 0.6) {
    console.log('⚠️ FAIR! Some functionality working, but improvements needed.');
  } else {
    console.log('❌ POOR! Major issues detected. Please review implementation.');
  }

  console.log('\n🔧 NEXT STEPS:');
  if (!testResults.authentication) {
    console.log('1. Sign in to test full functionality');
  }
  if (!testResults.moodBoardsDisplayed && testResults.authentication) {
    console.log('2. Create a mood board to test history display');
  }
  if (passedTests === totalTests) {
    console.log('1. Mood board functionality is ready for production!');
    console.log('2. Consider adding more advanced features');
  }

  return testResults;
}

// Auto-run the test
testMoodBoardComplete().catch(console.error);
