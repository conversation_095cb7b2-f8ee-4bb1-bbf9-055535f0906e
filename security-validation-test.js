/**
 * Comprehensive Security Validation Test for Mood Board Database
 * Tests RLS policies, user isolation, and authentication security
 * 
 * Run this in the browser console on: http://localhost:3002
 */

async function comprehensiveSecurityTest() {
  console.log('🔒 COMPREHENSIVE SECURITY VALIDATION TEST');
  console.log('='.repeat(60));
  
  const testResults = {
    authentication: false,
    userIsolation: false,
    rlsPolicies: false,
    dataLeakage: false,
    authorizationBypass: false,
    overallSecurityScore: 0
  };

  // Test 1: Authentication Security
  console.log('\n🔐 TEST 1: Authentication Security');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.error('❌ Authentication failed - please sign in first');
      return testResults;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      sessionValid: !!session,
      tokenPresent: !!session.access_token
    });
    
    testResults.authentication = true;
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return testResults;
  }

  // Test 2: User Isolation - Mood Boards
  console.log('\n🏠 TEST 2: User Isolation - Mood Boards');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user } } = await supabase.auth.getUser();
    
    // Test 2a: Can access own mood boards
    const { data: ownMoodboards, error: ownError } = await supabase
      .from('moodboards')
      .select('id, user_id, title, created_at')
      .eq('user_id', user.id)
      .limit(5);
    
    if (ownError) {
      console.error('❌ Cannot access own mood boards:', ownError);
      return testResults;
    }
    
    console.log('✅ Can access own mood boards:', {
      count: ownMoodboards?.length || 0,
      allBelongToUser: ownMoodboards?.every(mb => mb.user_id === user.id) || true
    });
    
    // Test 2b: Cannot access other users' mood boards (attempt to bypass RLS)
    const { data: allMoodboards, error: allError } = await supabase
      .from('moodboards')
      .select('id, user_id, title, created_at')
      .neq('user_id', user.id)  // Try to get other users' data
      .limit(5);
    
    if (allError) {
      console.log('✅ RLS blocking access to other users data (expected):', allError.message);
    } else if (!allMoodboards || allMoodboards.length === 0) {
      console.log('✅ No other users data accessible (good)');
    } else {
      console.error('🚨 SECURITY BREACH: Can access other users mood boards!', allMoodboards);
      return testResults;
    }
    
    testResults.userIsolation = true;
    
  } catch (error) {
    console.error('❌ User isolation test failed:', error);
    return testResults;
  }

  // Test 3: RLS Policy Enforcement
  console.log('\n🛡️ TEST 3: RLS Policy Enforcement');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user } } = await supabase.auth.getUser();
    
    // Test 3a: INSERT with correct user_id
    const testMoodboard = {
      title: 'Security Test Mood Board - ' + Date.now(),
      description: 'Test mood board for security validation',
      user_id: user.id,
      tldraw_data: { test: true },
      tags: ['security-test'],
      is_public: false,
      is_favorite: false
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('moodboards')
      .insert(testMoodboard)
      .select()
      .single();
    
    if (insertError) {
      console.error('❌ Cannot insert with correct user_id:', insertError);
      return testResults;
    }
    
    console.log('✅ Can insert with correct user_id:', insertData.id);
    const testMoodboardId = insertData.id;
    
    // Test 3b: Try to INSERT with different user_id (should fail)
    const maliciousInsert = {
      ...testMoodboard,
      title: 'Malicious Insert Test',
      user_id: 'fake-user-id-12345'  // Different user_id
    };
    
    const { data: maliciousData, error: maliciousError } = await supabase
      .from('moodboards')
      .insert(maliciousInsert)
      .select();
    
    if (maliciousError) {
      console.log('✅ RLS blocked malicious insert (expected):', maliciousError.message);
    } else {
      console.error('🚨 SECURITY BREACH: Malicious insert succeeded!', maliciousData);
      return testResults;
    }
    
    // Test 3c: Try to UPDATE another user's record (should fail)
    const { data: updateData, error: updateError } = await supabase
      .from('moodboards')
      .update({ title: 'Hacked Title' })
      .eq('id', testMoodboardId)
      .eq('user_id', 'fake-user-id')  // Wrong user_id
      .select();
    
    if (updateError || !updateData || updateData.length === 0) {
      console.log('✅ RLS blocked unauthorized update (expected)');
    } else {
      console.error('🚨 SECURITY BREACH: Unauthorized update succeeded!', updateData);
      return testResults;
    }
    
    // Clean up test data
    await supabase
      .from('moodboards')
      .delete()
      .eq('id', testMoodboardId)
      .eq('user_id', user.id);
    
    console.log('✅ Test data cleaned up');
    testResults.rlsPolicies = true;
    
  } catch (error) {
    console.error('❌ RLS policy test failed:', error);
    return testResults;
  }

  // Test 4: Data Leakage Prevention
  console.log('\n🔍 TEST 4: Data Leakage Prevention');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test 4a: Try to access without user filter
    const { data: leakData, error: leakError } = await supabase
      .from('moodboards')
      .select('id, user_id, title')
      .limit(10);  // No user filter - should only return current user's data
    
    if (leakError) {
      console.log('✅ Query without user filter blocked (expected):', leakError.message);
    } else if (leakData && leakData.length > 0) {
      const { data: { user } } = await supabase.auth.getUser();
      const allBelongToUser = leakData.every(item => item.user_id === user.id);
      
      if (allBelongToUser) {
        console.log('✅ Query without user filter only returns current user data');
      } else {
        console.error('🚨 DATA LEAKAGE: Query returned other users data!', leakData);
        return testResults;
      }
    } else {
      console.log('✅ No data leakage detected');
    }
    
    testResults.dataLeakage = true;
    
  } catch (error) {
    console.error('❌ Data leakage test failed:', error);
    return testResults;
  }

  // Test 5: Authorization Bypass Attempts
  console.log('\n🚫 TEST 5: Authorization Bypass Attempts');
  console.log('-'.repeat(50));
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Test 5a: Try SQL injection in user_id filter
    const { data: sqlData, error: sqlError } = await supabase
      .from('moodboards')
      .select('id, user_id, title')
      .eq('user_id', "'; DROP TABLE moodboards; --")
      .limit(1);
    
    if (sqlError) {
      console.log('✅ SQL injection attempt blocked (expected)');
    } else {
      console.log('✅ SQL injection attempt handled safely (no data returned)');
    }
    
    // Test 5b: Try to bypass with OR condition
    const { data: orData, error: orError } = await supabase
      .from('moodboards')
      .select('id, user_id, title')
      .or('user_id.eq.fake-id,user_id.neq.fake-id')  // Should still be filtered by RLS
      .limit(5);
    
    if (orError) {
      console.log('✅ OR bypass attempt blocked (expected)');
    } else if (orData) {
      const { data: { user } } = await supabase.auth.getUser();
      const allBelongToUser = orData.every(item => item.user_id === user.id);
      
      if (allBelongToUser) {
        console.log('✅ OR bypass attempt filtered by RLS');
      } else {
        console.error('🚨 AUTHORIZATION BYPASS: OR condition bypassed RLS!', orData);
        return testResults;
      }
    }
    
    testResults.authorizationBypass = true;
    
  } catch (error) {
    console.error('❌ Authorization bypass test failed:', error);
    return testResults;
  }

  // Calculate overall security score
  const totalTests = Object.keys(testResults).length - 1; // Exclude overallSecurityScore
  const passedTests = Object.values(testResults).filter(result => result === true).length;
  testResults.overallSecurityScore = Math.round((passedTests / totalTests) * 100);

  // Final Results
  console.log('\n📊 SECURITY TEST RESULTS');
  console.log('='.repeat(60));
  
  Object.entries(testResults).forEach(([test, result]) => {
    if (test !== 'overallSecurityScore') {
      const status = result ? '✅ SECURE' : '❌ VULNERABLE';
      const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`${status} ${testName}`);
    }
  });
  
  console.log('\n🎯 OVERALL SECURITY SCORE:', `${testResults.overallSecurityScore}%`);
  
  if (testResults.overallSecurityScore === 100) {
    console.log('\n🎉 EXCELLENT! All security tests passed!');
    console.log('✅ Database is properly secured for production use');
  } else if (testResults.overallSecurityScore >= 80) {
    console.log('\n⚠️ GOOD - Most security measures working, minor issues detected');
  } else {
    console.log('\n🚨 CRITICAL - Major security vulnerabilities detected!');
  }
  
  return testResults;
}

// Run the comprehensive security test
comprehensiveSecurityTest().catch(console.error);
