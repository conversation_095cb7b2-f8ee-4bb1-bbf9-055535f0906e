/**
 * Simple frontend test - run in browser console
 */

async function testSimple() {
  console.log('🧪 Simple Frontend Test');
  
  try {
    // Test Supabase connection
    const { supabase } = await import('/src/lib/supabase.ts');
    console.log('✅ Supabase imported');
    
    // Test auth
    const { data: { user } } = await supabase.auth.getUser();
    console.log('🔐 User:', user ? `${user.email} (${user.id})` : 'Not authenticated');
    
    // Test direct table access
    const { data, error } = await supabase
      .from('moodboards')
      .select('*')
      .limit(5);
      
    if (error) {
      console.error('❌ Database error:', error);
    } else {
      console.log('✅ Database query successful');
      console.log('📊 Found mood boards:', data.length);
      console.log('📋 Data:', data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSimple();
