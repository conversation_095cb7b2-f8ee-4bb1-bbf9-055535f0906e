/**
 * Execute Diagnostics for Visual Complexity Analyzer
 * This script runs all diagnostic tests and captures results
 * 
 * Copy and paste this entire script into the browser console on the Visual Complexity Analyzer page
 */

console.log('🚀 EXECUTING COMPREHENSIVE DIAGNOSTICS...');

// Wait for page to load completely
if (document.readyState !== 'complete') {
  console.log('⏳ Waiting for page to load...');
  window.addEventListener('load', runDiagnostics);
} else {
  runDiagnostics();
}

async function runDiagnostics() {
  console.log('🔬 Starting diagnostic execution...');
  
  const results = {
    timestamp: new Date().toISOString(),
    environment: {},
    authentication: {},
    storage: {},
    upload: {},
    errors: [],
    recommendations: []
  };

  try {
    // Step 1: Environment Check
    console.log('\n=== STEP 1: ENVIRONMENT CHECK ===');
    results.environment = await checkEnvironment();
    
    // Step 2: Authentication Check
    console.log('\n=== STEP 2: AUTHENTICATION CHECK ===');
    results.authentication = await checkAuthentication();
    
    if (!results.authentication.success) {
      console.error('❌ Cannot proceed without authentication');
      return results;
    }
    
    // Step 3: Storage Connection Test
    console.log('\n=== STEP 3: STORAGE CONNECTION TEST ===');
    results.storage = await testStorageConnection(results.authentication.user);
    
    // Step 4: Upload Tests
    console.log('\n=== STEP 4: UPLOAD TESTS ===');
    results.upload = await testUploads(results.authentication.user);
    
    // Step 5: Generate Report
    console.log('\n=== DIAGNOSTIC REPORT ===');
    generateReport(results);
    
    // Store results globally for inspection
    window.diagnosticResults = results;
    console.log('💾 Results stored in window.diagnosticResults');
    
    return results;
    
  } catch (error) {
    console.error('💥 Diagnostic execution failed:', error);
    results.errors.push(`Diagnostic execution failed: ${error.message}`);
    return results;
  }
}

async function checkEnvironment() {
  console.log('🌍 Checking environment...');
  
  const env = {
    url: window.location.href,
    supabaseAvailable: !!window.supabase,
    serviceAvailable: !!window.designAnalysisService,
    reactAvailable: !!window.React,
    userAgent: navigator.userAgent.substring(0, 100),
    success: false
  };
  
  console.log(`📍 URL: ${env.url}`);
  console.log(`🔧 Supabase: ${env.supabaseAvailable ? '✅' : '❌'}`);
  console.log(`⚙️ Service: ${env.serviceAvailable ? '✅' : '❌'}`);
  console.log(`⚛️ React: ${env.reactAvailable ? '✅' : '❌'}`);
  
  if (!env.supabaseAvailable) {
    console.error('❌ Supabase not available - check if client is loaded');
    env.error = 'Supabase not available';
    return env;
  }
  
  if (!env.serviceAvailable) {
    console.error('❌ designAnalysisService not available - check service initialization');
    env.error = 'designAnalysisService not available';
    return env;
  }
  
  env.success = true;
  console.log('✅ Environment check passed');
  return env;
}

async function checkAuthentication() {
  console.log('🔐 Checking authentication...');
  
  const auth = {
    success: false,
    user: null,
    error: null
  };
  
  try {
    const { data: { user }, error } = await window.supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Auth error:', error.message);
      auth.error = error.message;
      return auth;
    }
    
    if (!user) {
      console.error('❌ No authenticated user');
      auth.error = 'No authenticated user';
      return auth;
    }
    
    auth.user = {
      id: user.id,
      email: user.email,
      role: user.role || 'authenticated',
      tokenExpiry: new Date(user.exp * 1000).toISOString()
    };
    
    auth.success = true;
    
    console.log(`✅ Authenticated as: ${user.email}`);
    console.log(`👤 User ID: ${user.id}`);
    console.log(`🔑 Token expires: ${new Date(user.exp * 1000).toLocaleString()}`);
    
    return auth;
    
  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    auth.error = error.message;
    return auth;
  }
}

async function testStorageConnection(user) {
  console.log('🪣 Testing storage connection...');
  
  const storage = {
    bucketAccess: false,
    userFolderAccess: false,
    success: false,
    error: null
  };
  
  try {
    // Test bucket listing
    const { data: buckets, error: bucketError } = await window.supabase.storage.listBuckets();
    
    if (bucketError) {
      console.error('❌ Bucket list error:', bucketError.message);
      storage.error = `Bucket list error: ${bucketError.message}`;
      return storage;
    }
    
    const designBucket = buckets.find(b => b.name === 'design-analysis-images');
    if (!designBucket) {
      console.error('❌ design-analysis-images bucket not found');
      storage.error = 'design-analysis-images bucket not found';
      return storage;
    }
    
    storage.bucketAccess = true;
    console.log(`✅ Bucket found: ${designBucket.name} (${designBucket.public ? 'public' : 'private'})`);
    
    // Test user folder access
    const { data: files, error: listError } = await window.supabase.storage
      .from('design-analysis-images')
      .list(user.id, { limit: 1 });
    
    if (listError) {
      console.error('❌ User folder access error:', listError.message);
      storage.error = `User folder access error: ${listError.message}`;
      return storage;
    }
    
    storage.userFolderAccess = true;
    storage.success = true;
    console.log(`✅ User folder accessible: ${files.length} files found`);
    
    return storage;
    
  } catch (error) {
    console.error('❌ Storage connection failed:', error);
    storage.error = error.message;
    return storage;
  }
}

async function testUploads(user) {
  console.log('📤 Testing uploads...');
  
  const upload = {
    directUpload: { success: false, error: null },
    serviceUpload: { success: false, error: null },
    completeFlow: { success: false, error: null, analysisId: null }
  };
  
  // Test 1: Direct Supabase Upload
  console.log('🧪 Test 1: Direct Supabase upload...');
  try {
    const testContent = `test-${Date.now()}`;
    const testBlob = new Blob([testContent], { type: 'text/plain' });
    const testFile = new File([testBlob], 'direct-test.txt', { type: 'text/plain' });
    const fileName = `${user.id}/direct-test-${Date.now()}.txt`;
    
    console.log(`📝 Uploading to: ${fileName}`);
    
    const { data, error } = await window.supabase.storage
      .from('design-analysis-images')
      .upload(fileName, testFile, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) {
      console.error('❌ Direct upload failed:', error);
      upload.directUpload.error = error.message;
      
      if (error.message.includes('row-level security policy')) {
        console.error('🚨 RLS POLICY VIOLATION DETECTED');
      }
    } else {
      console.log('✅ Direct upload successful:', data.path);
      upload.directUpload.success = true;
      
      // Clean up
      await window.supabase.storage
        .from('design-analysis-images')
        .remove([fileName]);
    }
  } catch (error) {
    console.error('❌ Direct upload exception:', error);
    upload.directUpload.error = error.message;
  }
  
  // Test 2: Service Upload
  console.log('🧪 Test 2: Service upload...');
  try {
    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = 10;
    canvas.height = 10;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 10, 10);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const imageFile = new File([blob], 'service-test.png', { type: 'image/png' });
    
    console.log(`📷 Created test image: ${imageFile.name} (${imageFile.size} bytes)`);
    
    const result = await window.designAnalysisService.uploadImage(imageFile, user.id);
    
    if (!result) {
      console.error('❌ Service upload returned null');
      upload.serviceUpload.error = 'Service returned null';
    } else {
      console.log('✅ Service upload successful:', result);
      upload.serviceUpload.success = true;
      
      // Clean up
      await window.supabase.storage
        .from('design-analysis-images')
        .remove([result]);
    }
  } catch (error) {
    console.error('❌ Service upload failed:', error);
    upload.serviceUpload.error = error.message;
  }
  
  // Test 3: Complete Flow
  console.log('🧪 Test 3: Complete saveAnalysis flow...');
  try {
    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = 20;
    canvas.height = 20;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#00FF00';
    ctx.fillRect(0, 0, 20, 20);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const imageFile = new File([blob], 'complete-test.png', { type: 'image/png' });
    
    const testData = {
      user_id: user.id,
      tool_type: 'visual-complexity',
      original_filename: imageFile.name,
      file_size: imageFile.size,
      file_type: imageFile.type,
      overall_score: 85,
      complexity_scores: { color: 8, layout: 8, typography: 8, elements: 9 },
      analysis_areas: [{ name: 'Test', score: 8, description: 'Diagnostic test' }],
      recommendations: [{ category: 'Test', issue: 'Test', importance: 'alta', recommendation: 'Test' }],
      ai_analysis_summary: 'Diagnostic test analysis',
      agent_message: 'Testing complete flow',
      visuai_insights: 'Test insights',
      tags: ['diagnostic-test']
    };
    
    console.log('💾 Calling saveAnalysis...');
    
    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testData, imageFile);
    
    console.log(`✅ Analysis saved: ${savedAnalysis.id}`);
    console.log(`📁 File URL: ${savedAnalysis.file_url || 'NULL'}`);
    
    upload.completeFlow.analysisId = savedAnalysis.id;
    
    if (savedAnalysis.file_url) {
      upload.completeFlow.success = true;
      console.log('🎉 Complete flow successful - file_url populated!');
    } else {
      upload.completeFlow.error = 'file_url is null in saved analysis';
      console.error('❌ Complete flow failed - file_url is null');
    }
    
    // Clean up
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('🧹 Test analysis cleaned up');
    
  } catch (error) {
    console.error('❌ Complete flow failed:', error);
    upload.completeFlow.error = error.message;
  }
  
  return upload;
}

function generateReport(results) {
  console.log('📊 COMPREHENSIVE DIAGNOSTIC REPORT');
  console.log('=====================================');
  
  console.log(`🕐 Timestamp: ${results.timestamp}`);
  console.log(`🌍 Environment: ${results.environment.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🔐 Authentication: ${results.authentication.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🪣 Storage: ${results.storage.success ? '✅ PASS' : '❌ FAIL'}`);
  
  console.log('\n📤 UPLOAD TEST RESULTS:');
  console.log(`  Direct Upload: ${results.upload.directUpload.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Service Upload: ${results.upload.serviceUpload.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Complete Flow: ${results.upload.completeFlow.success ? '✅ PASS' : '❌ FAIL'}`);
  
  // Identify root cause
  console.log('\n🎯 ROOT CAUSE ANALYSIS:');
  
  if (!results.environment.success) {
    console.log('❌ ENVIRONMENT ISSUE: Missing required dependencies');
    results.recommendations.push('Fix environment setup - ensure Supabase and service are loaded');
  } else if (!results.authentication.success) {
    console.log('❌ AUTHENTICATION ISSUE: User not authenticated or token invalid');
    results.recommendations.push('Fix authentication - ensure user is logged in with valid token');
  } else if (!results.storage.success) {
    console.log('❌ STORAGE ACCESS ISSUE: Cannot access Supabase Storage');
    results.recommendations.push('Fix storage permissions - check RLS policies and bucket configuration');
  } else if (!results.upload.directUpload.success) {
    console.log('❌ DIRECT UPLOAD ISSUE: Basic Supabase upload failing');
    results.recommendations.push('Fix direct upload - check RLS policies, authentication, or network issues');
  } else if (!results.upload.serviceUpload.success) {
    console.log('❌ SERVICE UPLOAD ISSUE: designAnalysisService.uploadImage failing');
    results.recommendations.push('Fix service upload method - check implementation bugs');
  } else if (!results.upload.completeFlow.success) {
    console.log('❌ COMPLETE FLOW ISSUE: saveAnalysis not populating file_url');
    results.recommendations.push('Fix saveAnalysis method - check file_url assignment logic');
  } else {
    console.log('✅ ALL TESTS PASSED: Upload functionality is working correctly');
    results.recommendations.push('No issues found - upload functionality is working');
  }
  
  // Show specific errors
  if (results.errors.length > 0 || 
      results.upload.directUpload.error || 
      results.upload.serviceUpload.error || 
      results.upload.completeFlow.error) {
    
    console.log('\n🚨 SPECIFIC ERRORS:');
    
    if (results.environment.error) {
      console.log(`  Environment: ${results.environment.error}`);
    }
    if (results.authentication.error) {
      console.log(`  Authentication: ${results.authentication.error}`);
    }
    if (results.storage.error) {
      console.log(`  Storage: ${results.storage.error}`);
    }
    if (results.upload.directUpload.error) {
      console.log(`  Direct Upload: ${results.upload.directUpload.error}`);
    }
    if (results.upload.serviceUpload.error) {
      console.log(`  Service Upload: ${results.upload.serviceUpload.error}`);
    }
    if (results.upload.completeFlow.error) {
      console.log(`  Complete Flow: ${results.upload.completeFlow.error}`);
    }
  }
  
  console.log('\n💡 RECOMMENDATIONS:');
  results.recommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec}`);
  });
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Review the specific errors above');
  console.log('2. Apply the recommended fixes');
  console.log('3. Re-run this diagnostic to verify the fix');
  console.log('4. Test actual upload in the Visual Complexity Analyzer');
  
  console.log('\n🔍 Detailed results available in window.diagnosticResults');
}

// Auto-execute
console.log('🎬 Diagnostic script loaded. Executing...');
