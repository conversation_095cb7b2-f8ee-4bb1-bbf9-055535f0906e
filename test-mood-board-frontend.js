/**
 * Frontend test script for mood board functionality
 * Run this in the browser console on the mood board page
 */

async function testMoodBoardFrontend() {
  console.log('🎨 Testing Mood Board Frontend Functionality');
  console.log('=' * 50);

  try {
    // Test 1: Check authentication
    console.log('\n1️⃣ Testing Authentication...');
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.error('❌ Authentication failed:', authError);
      console.log('💡 Please make sure you are logged in to Emma Studio');
      return;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      hasToken: !!session.access_token
    });

    // Test 2: Test mood board service
    console.log('\n2️⃣ Testing Mood Board Service...');
    const { default: moodboardService } = await import('/src/services/moodboard-service.ts');
    
    // Create a test mood board
    const testMoodboard = {
      title: 'Frontend Test Mood Board',
      description: 'Testing mood board creation from frontend',
      tldraw_data: {
        shapes: [],
        bindings: [],
        assets: []
      },
      tags: ['test', 'frontend'],
      is_public: false,
      is_favorite: false,
      collaboration_enabled: false,
      shared_with: [],
      notes: 'Created via frontend test'
    };

    console.log('📝 Creating test mood board...');
    const createResult = await moodboardService.createMoodboard(testMoodboard);
    
    if (createResult.success) {
      console.log('✅ Mood board created successfully!');
      console.log('📄 Created mood board:', createResult.data);
      
      const moodboardId = createResult.data.id;
      
      // Test 3: List mood boards
      console.log('\n3️⃣ Testing Mood Board Listing...');
      const listResult = await moodboardService.listMoodboards();
      
      console.log('✅ Mood boards listed successfully!');
      console.log('📊 Found mood boards:', listResult.moodboards.length);
      console.log('📋 Mood boards:', listResult.moodboards.map(mb => ({
        id: mb.id,
        title: mb.title,
        created_at: mb.created_at
      })));
      
      // Test 4: Get specific mood board
      console.log('\n4️⃣ Testing Mood Board Retrieval...');
      const getResult = await moodboardService.getMoodboard(moodboardId);
      
      console.log('✅ Mood board retrieved successfully!');
      console.log('📄 Retrieved mood board:', {
        id: getResult.id,
        title: getResult.title,
        description: getResult.description
      });
      
      // Test 5: Update mood board
      console.log('\n5️⃣ Testing Mood Board Update...');
      const updateResult = await moodboardService.updateMoodboard(moodboardId, {
        title: 'Updated Frontend Test Mood Board',
        description: 'Updated description from frontend test'
      });
      
      if (updateResult.success) {
        console.log('✅ Mood board updated successfully!');
      } else {
        console.log('❌ Failed to update mood board:', updateResult.message);
      }
      
      // Test 6: Clean up - delete test mood board
      console.log('\n6️⃣ Cleaning up test data...');
      const deleteResult = await moodboardService.deleteMoodboard(moodboardId);
      
      if (deleteResult.success) {
        console.log('✅ Test mood board deleted successfully!');
      } else {
        console.log('❌ Failed to delete test mood board:', deleteResult.message);
      }
      
    } else {
      console.error('❌ Failed to create mood board:', createResult.message);
    }

    // Test 7: Check React hooks
    console.log('\n7️⃣ Testing React Hooks...');
    if (window.React && window.React.version) {
      console.log('✅ React is available:', window.React.version);
      
      // Check if useMoodboard hook is working
      const hookModule = await import('/src/hooks/use-moodboard.ts');
      console.log('✅ useMoodboard hook module loaded');
    } else {
      console.log('⚠️ React not detected in global scope');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('📄 Error details:', error.message);
    console.error('🔍 Stack trace:', error.stack);
  }

  console.log('\n' + '=' * 50);
  console.log('🏁 Frontend test completed!');
}

// Auto-run the test
testMoodBoardFrontend();
