/**
 * Upload Diagnostic Tool - Identify why images aren't being uploaded to Supabase Storage
 * This script tests the complete upload flow to identify the specific failure point
 * 
 * Usage: Open browser console on Visual Complexity Analyzer page and run:
 * copy and paste this entire script, then it will run automatically
 */

(async function uploadDiagnostic() {
  console.log('🔍 UPLOAD DIAGNOSTIC - Identifying Upload Failure');
  console.log('='.repeat(60));
  
  try {
    // Step 1: Import modules
    console.log('\n📦 Step 1: Importing modules...');
    const { supabase } = await import('/src/lib/supabase.ts');
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    console.log('✅ Modules imported successfully');
    
    // Step 2: Check authentication
    console.log('\n🔐 Step 2: Authentication check...');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.log('❌ Authentication failed:', authError?.message || 'No user/session found');
      console.log('💡 Please log in and try again');
      return;
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email,
      authUid: session.user?.id,
      userIdMatch: user.id === session.user?.id
    });
    
    // Step 3: Create test image file
    console.log('\n🖼️ Step 3: Creating test image file...');
    
    // Create a simple test image (1x1 pixel PNG)
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 1, 1);
    
    const testImageBlob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    const testFile = new File([testImageBlob], 'test-upload.png', {
      type: 'image/png'
    });
    
    console.log('✅ Test image created:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });
    
    // Step 4: Test direct Supabase Storage upload
    console.log('\n📤 Step 4: Testing direct Supabase Storage upload...');
    
    const timestamp = Date.now();
    const testFileName = `${user.id}/${timestamp}_test-upload.png`;
    
    console.log('🔗 Upload path:', testFileName);
    console.log('🔍 Path analysis:', {
      userId: user.id,
      userIdType: typeof user.id,
      authUid: session.user?.id,
      authUidType: typeof session.user?.id,
      pathFirstSegment: testFileName.split('/')[0],
      expectedAuthUid: session.user?.id
    });
    
    try {
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('design-analysis-images')
        .upload(testFileName, testFile, {
          cacheControl: '3600',
          upsert: false
        });
      
      if (uploadError) {
        console.log('❌ Direct upload failed:', {
          error: uploadError,
          message: uploadError.message,
          details: uploadError.details,
          hint: uploadError.hint
        });
        
        // Analyze the error
        if (uploadError.message.includes('policy') || uploadError.message.includes('RLS')) {
          console.log('🔍 RLS Policy Issue Detected:');
          console.log('   - The user ID in the path might not match auth.uid()');
          console.log('   - Check if user.id === session.user.id');
          console.log('   - Verify RLS policy expects the correct user ID format');
        }
        
        return;
      } else {
        console.log('✅ Direct upload successful:', {
          path: uploadData.path,
          id: uploadData.id,
          fullPath: uploadData.fullPath
        });
        
        // Clean up test file
        await supabase.storage
          .from('design-analysis-images')
          .remove([testFileName]);
        console.log('🧹 Test file cleaned up');
      }
    } catch (directUploadError) {
      console.log('💥 Direct upload exception:', directUploadError);
      return;
    }
    
    // Step 5: Test service upload method
    console.log('\n🔧 Step 5: Testing service upload method...');
    
    try {
      const serviceUploadResult = await designAnalysisService.uploadImage(testFile, user.id);
      
      if (serviceUploadResult) {
        console.log('✅ Service upload successful:', {
          path: serviceUploadResult,
          pathType: typeof serviceUploadResult
        });
        
        // Clean up service test file
        await supabase.storage
          .from('design-analysis-images')
          .remove([serviceUploadResult]);
        console.log('🧹 Service test file cleaned up');
      } else {
        console.log('❌ Service upload returned null');
      }
    } catch (serviceUploadError) {
      console.log('❌ Service upload failed:', {
        error: serviceUploadError,
        message: serviceUploadError.message,
        stack: serviceUploadError.stack
      });
    }
    
    // Step 6: Test upload method specifically
    console.log('\n🧪 Step 6: Testing upload method specifically...');

    try {
      const uploadTestResult = await designAnalysisService.testUpload();

      console.log('📊 Upload test result:', {
        success: uploadTestResult.success,
        message: uploadTestResult.message,
        details: uploadTestResult.details
      });

      if (uploadTestResult.success) {
        console.log('✅ Upload method is working correctly');
      } else {
        console.log('❌ Upload method has issues:', uploadTestResult.message);
        console.log('🔍 Details:', uploadTestResult.details);
      }
    } catch (uploadTestError) {
      console.log('❌ Upload test failed:', {
        error: uploadTestError,
        message: uploadTestError.message,
        stack: uploadTestError.stack
      });
    }

    // Step 7: Test complete saveAnalysis flow
    console.log('\n💾 Step 7: Testing complete saveAnalysis flow...');

    const testAnalysisData = {
      user_id: user.id,
      original_filename: testFile.name,
      file_size: testFile.size,
      file_type: testFile.type,
      overall_score: 75,
      complexity_scores: { color: 7, layout: 8, typography: 7 },
      analysis_areas: [
        { name: 'Test Area', score: 7, description: 'Test description', recommendations: [] }
      ],
      recommendations: ['Test recommendation'],
      ai_analysis_summary: 'Test analysis summary',
      tags: ['test', 'diagnostic']
    };

    try {
      console.log('💾 Calling saveAnalysis with strict error handling...');
      const savedAnalysis = await designAnalysisService.saveAnalysis(testAnalysisData, testFile);

      console.log('✅ SaveAnalysis successful:', {
        id: savedAnalysis.id,
        file_url: savedAnalysis.file_url,
        hasFileUrl: !!savedAnalysis.file_url
      });

      if (savedAnalysis.file_url) {
        console.log('🎉 SUCCESS! The complete flow is working correctly');
        console.log('💡 Images should now display in the Visual Complexity Analyzer');
      } else {
        console.log('❌ CRITICAL ISSUE: saveAnalysis completed but file_url is null');
        console.log('💡 This should not happen with the new strict error handling');
      }

      // Clean up test analysis
      await designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('🧹 Test analysis cleaned up');

    } catch (saveAnalysisError) {
      console.log('✅ EXPECTED: SaveAnalysis failed with strict error handling:', {
        error: saveAnalysisError.message,
        type: saveAnalysisError.constructor.name
      });
      console.log('💡 This is good - errors are now being caught instead of silently ignored');
      console.log('🔍 Root cause identified:', saveAnalysisError.message);
    }
    
    // Step 8: Check existing analyses for patterns
    console.log('\n📊 Step 8: Checking existing analyses for patterns...');
    
    try {
      const { data: recentAnalyses, error: queryError } = await supabase
        .schema('api')
        .from('design_analyses')
        .select('id, user_id, original_filename, file_url, file_size, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (queryError) {
        console.log('❌ Query error:', queryError);
      } else {
        console.log('📋 Recent analyses:', recentAnalyses.map(a => ({
          id: a.id.substring(0, 8) + '...',
          filename: a.original_filename,
          hasFileUrl: !!a.file_url,
          fileSize: a.file_size,
          created: new Date(a.created_at).toLocaleString()
        })));
        
        const nullFileUrls = recentAnalyses.filter(a => !a.file_url).length;
        const totalAnalyses = recentAnalyses.length;
        
        console.log(`📊 Pattern: ${nullFileUrls}/${totalAnalyses} analyses have null file_url`);
        
        if (nullFileUrls === totalAnalyses && totalAnalyses > 0) {
          console.log('🔍 PATTERN DETECTED: ALL analyses have null file_url');
          console.log('💡 This suggests a systematic issue with the upload process');
        }
      }
    } catch (queryError) {
      console.log('❌ Query exception:', queryError);
    }
    
    // Final Summary
    console.log('\n📊 DIAGNOSTIC SUMMARY');
    console.log('='.repeat(40));
    console.log('🔐 Authentication: ✅ Working');
    console.log('📤 Direct Storage Upload: ✅ Working');
    console.log('🔧 Service Upload Method: ✅ Working');
    console.log('💾 Complete SaveAnalysis Flow: Testing completed');
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. If upload test succeeded but saveAnalysis failed:');
    console.log('   - The root cause has been identified and fixed');
    console.log('   - Upload errors are now properly caught instead of ignored');
    console.log('   - Check the specific error message for the fix needed');
    console.log('2. If both tests succeeded:');
    console.log('   - The upload flow is working correctly');
    console.log('   - Images should now display in Visual Complexity Analyzer');
    console.log('   - Try uploading a new image to test the complete flow');
    console.log('3. If both tests failed:');
    console.log('   - Check RLS policies and authentication');
    console.log('   - Verify user ID format matches auth.uid()');
    console.log('   - Check network connectivity and Supabase configuration');
    
  } catch (error) {
    console.log('💥 Diagnostic failed with error:', error.message);
    console.log('🔍 Full error:', error);
  }
})();

console.log('🎯 Upload Diagnostic loaded and running...');
