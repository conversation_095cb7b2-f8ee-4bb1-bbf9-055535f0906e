# Mood Board Manual Test Guide

## Quick Test Steps

### 1. Access the Mood Board Editor
1. Open your browser and navigate to: `http://localhost:3002`
2. Sign in to your account
3. Go to: `http://localhost:3002/dashboard/herramientas/mood-board/editor/new`

### 2. Create Content
1. **Add Shapes**: Use the toolbar to add rectangles, circles, arrows
2. **Add Text**: Click the text tool and add some text
3. **Add Images**: 
   - Drag and drop an image file onto the canvas
   - Or copy an image from your clipboard and paste it
4. **Style Elements**: Change colors, sizes, positions

### 3. Test Save Functionality
1. **Manual Save**: Click the "Crear" button in the top toolbar
2. **Verify Success**: You should see a success toast message
3. **Check URL**: The URL should change to include the mood board ID
4. **Auto-save**: Make changes and wait 30 seconds - you should see "Guardando..." indicator

### 4. Test State Preservation
1. **Refresh Page**: Reload the browser page
2. **Verify Content**: All your content should be exactly as you left it
3. **Test Navigation**: Go back to the mood board list and return to your board

### 5. Test User Isolation
1. **Create Multiple Boards**: Create several mood boards
2. **Check List**: Go to the mood board list - you should only see your own boards
3. **Test Access**: Try to access another user's board (should be denied)

## Expected Results

### ✅ What Should Work
- **Instant Saving**: Click "Crear" → Success message → URL updates
- **Auto-save**: Changes saved automatically every 30 seconds
- **Complete State**: All images, shapes, text, positions preserved exactly
- **User Isolation**: Only your mood boards visible and accessible
- **Error Handling**: Clear error messages if something goes wrong
- **Loading States**: Spinner and "Guardando..." indicators during saves

### ❌ What Should NOT Happen
- **No Duplicate Saves**: Button should be disabled during save operations
- **No Data Loss**: All content should persist through page refreshes
- **No Access to Other Users**: Cannot see or modify other users' mood boards
- **No Silent Failures**: All errors should show user-friendly messages

## Automated Test Scripts

### Run in Browser Console

1. **Basic Functionality Test**:
   ```javascript
   // Copy and paste the content of test-moodboard-functionality.js
   ```

2. **Complete Functionality Test**:
   ```javascript
   // Copy and paste the content of test-complete-moodboard-functionality.js
   ```

3. **State Capture Test**:
   ```javascript
   // Copy and paste the content of test-tldraw-state-capture.js
   ```

## Troubleshooting

### Common Issues and Solutions

#### "User not authenticated" Error
- **Solution**: Make sure you are signed in to your account
- **Check**: Look for user info in the top right corner

#### Save Button Not Working
- **Check**: Make sure both frontend (port 3002) and backend (port 8000) are running
- **Verify**: Open browser dev tools and check for network errors

#### Content Not Persisting
- **Check**: Verify you clicked "Crear" to save (not just auto-save)
- **Verify**: Check the URL includes a mood board ID after saving

#### Images Not Loading
- **Note**: This is expected and optimal - images are embedded in the mood board state
- **Verify**: Images should display correctly when you add them to the canvas

## Performance Expectations

### Normal Behavior
- **Save Time**: 1-3 seconds for typical mood boards
- **Load Time**: 1-2 seconds to load existing mood boards
- **Auto-save**: Triggers 30 seconds after last change
- **File Size**: Mood boards with images may be several MB (this is normal)

### When to Be Concerned
- **Save Time > 10 seconds**: Check network connection and backend logs
- **Frequent Errors**: Check browser console for JavaScript errors
- **Missing Content**: Verify save completed successfully before navigating away

## Technical Verification

### Database Check (Advanced)
If you have database access, you can verify:

```sql
-- Check your mood boards
SELECT id, title, created_at, updated_at, 
       LENGTH(tldraw_data::text) as state_size_bytes
FROM moodboards 
WHERE user_id = 'your-user-id'
ORDER BY created_at DESC;

-- Verify RLS policies
SELECT * FROM pg_policies WHERE tablename = 'moodboards';
```

### Network Check
In browser dev tools (F12):
1. Go to Network tab
2. Save a mood board
3. Look for POST request to `/api/moodboard/create` or `/api/moodboard/{id}/update`
4. Verify response is 200 OK

## Success Criteria

The mood board saving functionality is working correctly if:

1. ✅ **Authentication**: User can sign in and access the editor
2. ✅ **Content Creation**: Can add shapes, text, and images to the canvas
3. ✅ **Manual Save**: "Crear/Guardar" button works and shows success message
4. ✅ **Auto-save**: Changes are automatically saved every 30 seconds
5. ✅ **State Preservation**: All content persists exactly through page refreshes
6. ✅ **User Isolation**: Only user's own mood boards are visible and accessible
7. ✅ **Error Handling**: Clear error messages for any issues
8. ✅ **Performance**: Saves complete within reasonable time (< 5 seconds)

If all criteria are met, the implementation is ready for production use!
