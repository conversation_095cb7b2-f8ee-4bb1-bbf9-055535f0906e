import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { designAnalysisService } from '@/services/designAnalysisService';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface TestResult {
  step: string;
  success: boolean;
  message: string;
  details?: any;
}

export function ImageDisplayTest() {
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [testImageUrl, setTestImageUrl] = useState<string | null>(null);

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
  };

  const runComprehensiveTest = async () => {
    setIsRunning(true);
    setResults([]);
    setTestImageUrl(null);

    try {
      // Test 1: Authentication
      addResult({ step: 'Authentication', success: false, message: 'Testing...' });
      
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();
      if (authError || !authUser) {
        addResult({ 
          step: 'Authentication', 
          success: false, 
          message: `Failed: ${authError?.message || 'No user'}` 
        });
        return;
      }
      
      addResult({ 
        step: 'Authentication', 
        success: true, 
        message: `Success: User ${authUser.email}`,
        details: { userId: authUser.id }
      });

      // Test 2: Storage Connection
      addResult({ step: 'Storage Connection', success: false, message: 'Testing...' });
      
      const storageTest = await designAnalysisService.testStorageConnection(authUser.id);
      addResult({ 
        step: 'Storage Connection', 
        success: storageTest.success, 
        message: storageTest.message 
      });

      if (!storageTest.success) return;

      // Test 3: List Files
      addResult({ step: 'File Listing', success: false, message: 'Testing...' });
      
      const { data: files, error: listError } = await supabase.storage
        .from('design-analysis-images')
        .list(authUser.id, { limit: 5 });

      if (listError) {
        addResult({ 
          step: 'File Listing', 
          success: false, 
          message: `Failed: ${listError.message}` 
        });
        return;
      }

      addResult({ 
        step: 'File Listing', 
        success: true, 
        message: `Found ${files?.length || 0} files`,
        details: { files: files?.map(f => f.name) }
      });

      // Test 4: Image Retrieval (if files exist)
      if (files && files.length > 0) {
        const testFile = files[0];
        const filePath = `${authUser.id}/${testFile.name}`;
        
        addResult({ step: 'Image Retrieval', success: false, message: 'Testing...' });
        
        const imageUrl = await designAnalysisService.getImageUrl(filePath);
        
        if (imageUrl) {
          addResult({ 
            step: 'Image Retrieval', 
            success: true, 
            message: `Generated ${imageUrl.startsWith('blob:') ? 'blob' : 'http'} URL`,
            details: { url: imageUrl.substring(0, 50) + '...' }
          });

          // Test 5: Image Loading
          addResult({ step: 'Image Loading', success: false, message: 'Testing...' });
          
          const loadTest = await new Promise<boolean>((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = imageUrl;
            setTimeout(() => resolve(false), 10000);
          });

          addResult({ 
            step: 'Image Loading', 
            success: loadTest, 
            message: loadTest ? 'Image loads successfully' : 'Image failed to load' 
          });

          if (loadTest) {
            setTestImageUrl(imageUrl);
          } else if (imageUrl.startsWith('blob:')) {
            URL.revokeObjectURL(imageUrl);
          }
        } else {
          addResult({ 
            step: 'Image Retrieval', 
            success: false, 
            message: 'Failed to generate image URL' 
          });
        }
      } else {
        addResult({ 
          step: 'Image Retrieval', 
          success: true, 
          message: 'No files to test (upload an image first)' 
        });
      }

      // Test 6: Complete Flow Test
      addResult({ step: 'Complete Flow', success: false, message: 'Testing...' });
      
      const flowTest = await designAnalysisService.testImageFlow();
      addResult({ 
        step: 'Complete Flow', 
        success: flowTest.success, 
        message: flowTest.message,
        details: flowTest.details
      });

    } catch (error) {
      addResult({ 
        step: 'Test Error', 
        success: false, 
        message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown'}` 
      });
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setResults([]);
    if (testImageUrl && testImageUrl.startsWith('blob:')) {
      URL.revokeObjectURL(testImageUrl);
    }
    setTestImageUrl(null);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (testImageUrl && testImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(testImageUrl);
      }
    };
  }, [testImageUrl]);

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Image Display Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Please log in to run image display tests.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Image Display Diagnostic Tool</CardTitle>
        <p className="text-sm text-gray-600">
          Test the complete image upload, storage, and display flow
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={runComprehensiveTest} 
            disabled={isRunning}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isRunning ? 'Running Tests...' : 'Run Diagnostic'}
          </Button>
          <Button 
            onClick={clearResults} 
            variant="outline"
            disabled={isRunning}
          >
            Clear Results
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Test Results:</h3>
            {results.map((result, index) => (
              <div 
                key={index}
                className={`p-3 rounded border ${
                  result.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${
                    result.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {result.success ? '✅' : '❌'} {result.step}
                  </span>
                </div>
                <p className="text-sm mt-1">{result.message}</p>
                {result.details && (
                  <details className="mt-2">
                    <summary className="text-xs cursor-pointer">Details</summary>
                    <pre className="text-xs mt-1 bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {testImageUrl && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Test Image Display:</h3>
            <div className="border rounded p-4 bg-gray-50">
              <img 
                src={testImageUrl} 
                alt="Test image" 
                className="max-w-full max-h-64 object-contain"
                onError={() => {
                  console.error('Test image failed to display');
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
