// Comprehensive fix for Visual Complexity Analyzer image display issues
// This script identifies and fixes the root cause of missing images

console.log('🔧 Visual Complexity Analyzer - Image Display Issue Fix');

async function identifyRootCause() {
  console.log('\n🔍 Step 1: Identifying Root Cause...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('❌ No authenticated user');
      return false;
    }
    
    // Check analyses in database
    const { data: analyses, error: dbError } = await supabase
      .from('design_analyses')
      .select('id, user_id, original_filename, file_url, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (dbError) {
      console.error('❌ Database query failed:', dbError);
      return false;
    }
    
    console.log(`📊 Found ${analyses.length} analyses for user`);
    
    const analysesWithNullFileUrl = analyses.filter(a => a.file_url === null);
    const analysesWithFileUrl = analyses.filter(a => a.file_url !== null);
    
    console.log(`📊 Analyses with null file_url: ${analysesWithNullFileUrl.length}`);
    console.log(`📊 Analyses with file_url: ${analysesWithFileUrl.length}`);
    
    // Check storage for orphaned files
    const { data: storageFiles, error: storageError } = await supabase.storage
      .from('design-analysis-images')
      .list(user.id, { limit: 50 });
    
    if (storageError) {
      console.error('❌ Storage list failed:', storageError);
      return false;
    }
    
    console.log(`📁 Found ${storageFiles?.length || 0} files in storage`);
    
    // ROOT CAUSE ANALYSIS
    if (analysesWithNullFileUrl.length > 0 && storageFiles && storageFiles.length > 0) {
      console.log('\n🚨 ROOT CAUSE IDENTIFIED:');
      console.log('- Images are being uploaded to Supabase Storage successfully');
      console.log('- Analysis records are being saved to database');
      console.log('- BUT file_url is not being saved to the database');
      console.log('- This creates orphaned files in storage with no database references');
      
      return {
        issue: 'file_url_not_saved',
        orphanedFiles: storageFiles.length,
        affectedAnalyses: analysesWithNullFileUrl.length,
        workingAnalyses: analysesWithFileUrl.length
      };
    } else if (analysesWithNullFileUrl.length > 0 && (!storageFiles || storageFiles.length === 0)) {
      console.log('\n🚨 ROOT CAUSE IDENTIFIED:');
      console.log('- Image uploads are failing completely');
      console.log('- No files found in storage');
      
      return {
        issue: 'upload_failing',
        orphanedFiles: 0,
        affectedAnalyses: analysesWithNullFileUrl.length,
        workingAnalyses: analysesWithFileUrl.length
      };
    } else {
      console.log('\n✅ No issues detected - all analyses have file_url values');
      return {
        issue: 'none',
        orphanedFiles: storageFiles?.length || 0,
        affectedAnalyses: 0,
        workingAnalyses: analysesWithFileUrl.length
      };
    }
    
  } catch (error) {
    console.error('💥 Root cause analysis failed:', error);
    return false;
  }
}

async function attemptOrphanedFileRecovery() {
  console.log('\n🔄 Step 2: Attempting Orphaned File Recovery...');
  
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('❌ No authenticated user');
      return false;
    }
    
    // Get analyses with null file_url
    const { data: analyses } = await supabase
      .from('design_analyses')
      .select('id, original_filename, created_at, file_url')
      .eq('user_id', user.id)
      .is('file_url', null)
      .order('created_at', { ascending: false });
    
    // Get storage files
    const { data: storageFiles } = await supabase.storage
      .from('design-analysis-images')
      .list(user.id, { limit: 50 });
    
    if (!analyses || !storageFiles || analyses.length === 0 || storageFiles.length === 0) {
      console.log('⚠️ No orphaned files to recover');
      return true;
    }
    
    console.log(`🔍 Attempting to match ${analyses.length} analyses with ${storageFiles.length} storage files`);
    
    let recoveredCount = 0;
    
    // Try to match files by filename and timestamp
    for (const analysis of analyses) {
      const analysisDate = new Date(analysis.created_at);
      const analysisFilename = analysis.original_filename;
      
      // Look for storage files that might match this analysis
      const potentialMatches = storageFiles.filter(file => {
        const fileDate = new Date(file.created_at);
        const timeDiff = Math.abs(fileDate.getTime() - analysisDate.getTime());
        const timeDiffMinutes = timeDiff / (1000 * 60);
        
        // Match if created within 10 minutes and filename contains similar text
        return timeDiffMinutes <= 10 && (
          file.name.includes(analysisFilename.split('.')[0]) ||
          analysisFilename.includes(file.name.split('_').slice(1).join('_').split('.')[0])
        );
      });
      
      if (potentialMatches.length > 0) {
        const bestMatch = potentialMatches[0]; // Take the first match
        const filePath = `${user.id}/${bestMatch.name}`;
        
        console.log(`🔗 Attempting to link analysis ${analysis.id} with file ${bestMatch.name}`);
        
        // Update the analysis with the file path
        const { error: updateError } = await supabase
          .from('design_analyses')
          .update({ file_url: filePath })
          .eq('id', analysis.id);
        
        if (updateError) {
          console.error(`❌ Failed to update analysis ${analysis.id}:`, updateError);
        } else {
          console.log(`✅ Successfully linked analysis ${analysis.id} with file ${bestMatch.name}`);
          recoveredCount++;
        }
      }
    }
    
    console.log(`🎉 Recovery complete: ${recoveredCount} files recovered out of ${analyses.length} attempts`);
    return recoveredCount;
    
  } catch (error) {
    console.error('💥 Orphaned file recovery failed:', error);
    return false;
  }
}

async function testImageDisplayAfterFix() {
  console.log('\n🧪 Step 3: Testing Image Display After Fix...');
  
  try {
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('❌ No authenticated user');
      return false;
    }
    
    // Get analyses with file_url
    const { data: analyses } = await supabase
      .from('design_analyses')
      .select('id, original_filename, file_url')
      .eq('user_id', user.id)
      .not('file_url', 'is', null)
      .limit(3);
    
    if (!analyses || analyses.length === 0) {
      console.log('⚠️ No analyses with file_url to test');
      return true;
    }
    
    console.log(`🧪 Testing image display for ${analyses.length} analyses`);
    
    let successCount = 0;
    
    for (const analysis of analyses) {
      console.log(`\n🔍 Testing analysis: ${analysis.id}`);
      console.log(`📁 File path: ${analysis.file_url}`);
      
      try {
        const imageUrl = await designAnalysisService.getImageUrl(analysis.file_url);
        
        if (imageUrl) {
          console.log(`✅ Image URL generated successfully for ${analysis.id}`);
          console.log(`🔗 URL type: ${imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL'}`);
          
          // Test if the image actually loads
          const testImg = new Image();
          const loadPromise = new Promise((resolve, reject) => {
            testImg.onload = () => resolve(true);
            testImg.onerror = () => reject(new Error('Image failed to load'));
            setTimeout(() => reject(new Error('Image load timeout')), 5000);
          });
          
          testImg.src = imageUrl;
          
          try {
            await loadPromise;
            console.log(`✅ Image loads successfully for ${analysis.id}`);
            successCount++;
          } catch (loadError) {
            console.error(`❌ Image load failed for ${analysis.id}:`, loadError.message);
          }
          
          // Clean up object URL
          if (imageUrl.startsWith('blob:')) {
            URL.revokeObjectURL(imageUrl);
          }
        } else {
          console.error(`❌ Failed to generate image URL for ${analysis.id}`);
        }
      } catch (error) {
        console.error(`❌ Error testing ${analysis.id}:`, error);
      }
    }
    
    console.log(`\n📊 Image display test results: ${successCount}/${analyses.length} successful`);
    return successCount === analyses.length;
    
  } catch (error) {
    console.error('💥 Image display test failed:', error);
    return false;
  }
}

async function runCompleteFix() {
  console.log('🚀 Starting Complete Image Display Fix...\n');
  
  const results = {
    rootCause: null,
    recoveredFiles: 0,
    imageDisplayWorking: false
  };
  
  // Step 1: Identify root cause
  results.rootCause = await identifyRootCause();
  
  if (results.rootCause && results.rootCause.issue === 'file_url_not_saved') {
    // Step 2: Attempt recovery
    results.recoveredFiles = await attemptOrphanedFileRecovery();
    
    // Step 3: Test image display
    results.imageDisplayWorking = await testImageDisplayAfterFix();
  } else if (results.rootCause && results.rootCause.issue === 'upload_failing') {
    console.log('\n⚠️ Upload failure detected - this requires fixing the upload process');
    results.imageDisplayWorking = false;
  } else if (results.rootCause && results.rootCause.issue === 'none') {
    // Step 3: Test image display
    results.imageDisplayWorking = await testImageDisplayAfterFix();
  }
  
  console.log('\n📋 Fix Results Summary:');
  console.log('='.repeat(50));
  console.log('Root Cause:', results.rootCause ? results.rootCause.issue : 'Analysis failed');
  console.log('Files Recovered:', results.recoveredFiles || 0);
  console.log('Image Display Working:', results.imageDisplayWorking ? '✅' : '❌');
  
  if (results.rootCause && results.rootCause.issue === 'file_url_not_saved') {
    console.log('\n💡 Next Steps:');
    console.log('1. The saveAnalysis method needs debugging to ensure file_url is saved');
    console.log('2. Test the complete upload and save flow with a new analysis');
    console.log('3. Monitor the console logs during analysis saving');
    
    if (results.recoveredFiles > 0) {
      console.log(`4. ${results.recoveredFiles} orphaned files have been recovered`);
      console.log('5. Refresh the Visual Complexity Analyzer to see recovered images');
    }
  } else if (results.rootCause && results.rootCause.issue === 'upload_failing') {
    console.log('\n💡 Next Steps:');
    console.log('1. Check Supabase Storage permissions and RLS policies');
    console.log('2. Verify authentication tokens are valid');
    console.log('3. Test the uploadImage method directly');
  }
  
  return results;
}

// Auto-run the complete fix
runCompleteFix().then(results => {
  console.log('\n🏁 Image display fix completed');
  
  if (results.imageDisplayWorking) {
    console.log('🎉 SUCCESS: Images should now display correctly in the Visual Complexity Analyzer!');
    console.log('💡 Try refreshing the page and checking the history tab');
  } else {
    console.log('⚠️ Additional work needed - check the console logs above for specific issues');
  }
}).catch(error => {
  console.error('💥 Fix process failed:', error);
});

// Export functions for manual use
window.fixImageDisplay = {
  runCompleteFix,
  identifyRootCause,
  attemptOrphanedFileRecovery,
  testImageDisplayAfterFix
};

console.log('\n📝 Available fix functions:');
console.log('- window.fixImageDisplay.runCompleteFix()');
console.log('- window.fixImageDisplay.identifyRootCause()');
console.log('- window.fixImageDisplay.attemptOrphanedFileRecovery()');
console.log('- window.fixImageDisplay.testImageDisplayAfterFix()');
