// Simple test to verify Visual Complexity Analyzer fix
// Run this in browser console on the Visual Complexity Analyzer page

console.log('🧪 Testing Visual Complexity Analyzer Fix');

async function testFix() {
  try {
    // Check if user is authenticated
    const { data: { user } } = await window.supabase.auth.getUser();
    if (!user) {
      console.error('❌ Please log in first');
      return;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Create a simple test image
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    // Draw a simple colorful design
    ctx.fillStyle = '#FF6B6B';
    ctx.fillRect(0, 0, 100, 100);
    ctx.fillStyle = '#4ECDC4';
    ctx.fillRect(100, 0, 100, 100);
    ctx.fillStyle = '#45B7D1';
    ctx.fillRect(0, 100, 100, 100);
    ctx.fillStyle = '#96CEB4';
    ctx.fillRect(100, 100, 100, 100);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const testFile = new File([blob], 'test-fix.png', { type: 'image/png' });
    
    console.log('📸 Test image created:', testFile.name, testFile.size, 'bytes');
    
    // Test the backend endpoint directly
    const formData = new FormData();
    formData.append('design', testFile);
    
    const session = await window.supabase.auth.getSession();
    
    console.log('🚀 Sending request to backend...');
    const response = await fetch('http://localhost:5001/api/design-analysis/analyze', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.data.session.access_token}`
      },
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`Backend request failed: ${response.status}`);
    }
    
    const result = await response.json();
    console.log('📊 Backend response:', {
      success: result.success,
      analysisId: result.analysis_id,
      savedToDatabase: result.saved_to_database,
      score: result.score
    });
    
    if (!result.analysis_id) {
      console.error('❌ No analysis_id returned');
      return;
    }
    
    // Check the database record
    console.log('🔍 Checking database record...');
    const { data: dbRecord, error } = await window.supabase
      .from('design_analyses')
      .select('id, file_url, original_filename')
      .eq('id', result.analysis_id)
      .single();
    
    if (error) {
      console.error('❌ Database query failed:', error);
      return;
    }
    
    console.log('💾 Database record:', {
      id: dbRecord.id,
      hasFileUrl: !!dbRecord.file_url,
      fileUrl: dbRecord.file_url,
      filename: dbRecord.original_filename
    });
    
    // THE CRITICAL TEST: Check if file_url is populated
    if (dbRecord.file_url) {
      console.log('🎉 SUCCESS: file_url is populated!');
      console.log('✅ FIX VERIFIED: Images are now properly stored');
      
      // Test image retrieval
      try {
        const imageUrl = await window.designAnalysisService.getImageUrl(dbRecord.file_url);
        if (imageUrl) {
          console.log('🖼️ Image retrieval also works!');
          if (imageUrl.startsWith('blob:')) {
            URL.revokeObjectURL(imageUrl);
          }
        }
      } catch (retrievalError) {
        console.warn('⚠️ Image retrieval failed:', retrievalError.message);
      }
    } else {
      console.error('❌ FAILED: file_url is still NULL');
      console.error('❌ FIX NOT WORKING: Issue persists');
    }
    
    // Cleanup
    console.log('🧹 Cleaning up test data...');
    await window.designAnalysisService.deleteAnalysis(result.analysis_id);
    console.log('✅ Cleanup complete');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testFix();
