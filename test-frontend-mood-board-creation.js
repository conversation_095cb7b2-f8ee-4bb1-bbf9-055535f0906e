/**
 * Test script to create a mood board through the frontend and verify it appears in the list
 * Run this in the browser console on http://localhost:3002/dashboard/herramientas/mood-board
 */

async function testFrontendMoodBoardCreation() {
  console.log('🎨 Testing Frontend Mood Board Creation and Display...\n');
  
  const testResults = {
    authentication: false,
    moodboardCreation: false,
    moodboardListing: false,
    dataDisplay: false,
    frontendIntegration: false
  };

  // Test 1: Check Authentication
  console.log('🔐 Test 1: Authentication...');
  try {
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ User not authenticated');
      console.log('💡 Please sign in at: http://localhost:3002/login');
      return testResults;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      sessionValid: !!session
    });
    testResults.authentication = true;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return testResults;
  }

  // Test 2: Create a Test Mood Board
  console.log('\n🎨 Test 2: Creating Test Mood Board...');
  try {
    const moodboardService = await import('/src/services/moodboard-service.ts');
    
    const testMoodboardData = {
      title: `Test Mood Board ${Date.now()}`,
      description: 'A test mood board created through the frontend to verify functionality',
      tags: ['test', 'frontend', 'verification'],
      tldraw_data: {
        shapes: [
          {
            id: 'test-shape-1',
            type: 'text',
            props: {
              text: 'Frontend Test'
            }
          }
        ],
        bindings: [],
        assets: []
      },
      is_public: false,
      collaboration_enabled: false
    };
    
    console.log('📝 Creating mood board with data:', testMoodboardData);
    
    const createResponse = await moodboardService.default.createMoodboard(testMoodboardData);
    
    if (createResponse.success) {
      console.log('✅ Mood board created successfully:', {
        id: createResponse.data?.id,
        title: createResponse.data?.title
      });
      testResults.moodboardCreation = true;
    } else {
      console.log('❌ Failed to create mood board:', createResponse.message);
      return testResults;
    }
  } catch (error) {
    console.error('❌ Mood board creation failed:', error);
    return testResults;
  }

  // Test 3: List Mood Boards
  console.log('\n📋 Test 3: Listing Mood Boards...');
  try {
    const moodboardService = await import('/src/services/moodboard-service.ts');
    
    // Wait a moment for the creation to be processed
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const listResponse = await moodboardService.default.listMoodboards(1, 20, 'active');
    
    console.log('📊 List response:', {
      totalCount: listResponse.total_count,
      moodboardsFound: listResponse.moodboards?.length || 0,
      page: listResponse.page,
      limit: listResponse.limit
    });
    
    if (listResponse.moodboards && listResponse.moodboards.length > 0) {
      console.log('✅ Mood boards found in list');
      testResults.moodboardListing = true;
      
      // Show details of found mood boards
      listResponse.moodboards.forEach((mb, index) => {
        console.log(`📄 Mood Board ${index + 1}:`, {
          id: mb.id,
          title: mb.title,
          description: mb.description,
          tags: mb.tags,
          created: mb.created_at,
          updated: mb.updated_at
        });
      });
      
      testResults.dataDisplay = true;
    } else {
      console.log('❌ No mood boards found in list');
    }
  } catch (error) {
    console.error('❌ Mood board listing failed:', error);
    return testResults;
  }

  // Test 4: Check Frontend Integration
  console.log('\n🔗 Test 4: Frontend Integration...');
  try {
    // Check if the React Query hook is working
    const { useMoodboard } = await import('/src/hooks/use-moodboard.ts');
    
    // Check if the mood board list component is properly displaying data
    const moodboardCards = document.querySelectorAll('.grid.gap-6 > *');
    const loadingState = document.querySelector('.animate-spin');
    const emptyState = document.querySelector('.text-center.py-12');
    
    if (loadingState) {
      console.log('⏳ Loading state detected - data is being fetched');
    } else if (moodboardCards.length > 0) {
      console.log('✅ Mood board cards found in UI:', moodboardCards.length);
      testResults.frontendIntegration = true;
    } else if (emptyState) {
      console.log('📭 Empty state displayed - no mood boards in UI');
    } else {
      console.log('⚠️ UI state unclear');
    }
  } catch (error) {
    console.error('❌ Frontend integration test failed:', error);
  }

  // Test 5: Force Refresh Data
  console.log('\n🔄 Test 5: Force Refresh Data...');
  try {
    // Try to trigger a refresh of the mood board list
    if (window.location.pathname.includes('mood-board')) {
      console.log('🔄 Refreshing page to see updated data...');
      window.location.reload();
    }
  } catch (error) {
    console.error('❌ Refresh test failed:', error);
  }

  // Summary
  console.log('\n📊 FRONTEND TEST SUMMARY:');
  console.log('=========================');
  Object.entries(testResults).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed (${Math.round(passedTests/totalTests*100)}%)`);
  
  if (passedTests >= totalTests * 0.8) {
    console.log('🎉 EXCELLENT! Frontend mood board functionality is working!');
  } else if (passedTests >= totalTests * 0.6) {
    console.log('✅ GOOD! Most functionality working, minor issues may remain.');
  } else {
    console.log('⚠️ ISSUES! Frontend integration may have problems.');
  }

  console.log('\n🔧 NEXT STEPS:');
  if (testResults.moodboardCreation && testResults.moodboardListing) {
    console.log('1. ✅ Backend API is working correctly');
    console.log('2. ✅ Frontend can create and list mood boards');
    console.log('3. 🔄 Check if UI components are properly updating');
  } else if (testResults.authentication) {
    console.log('1. ✅ Authentication is working');
    console.log('2. ❌ Check backend API endpoints');
    console.log('3. ❌ Verify Supabase configuration');
  } else {
    console.log('1. ❌ Sign in to test functionality');
  }

  return testResults;
}

// Auto-run the test
testFrontendMoodBoardCreation().catch(console.error);
