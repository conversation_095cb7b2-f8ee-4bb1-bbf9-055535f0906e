// Verification script for Mood Board Editor fix
console.log('🔍 Verificando corrección del Mood Board Editor');

function checkConsoleErrors() {
  console.log('\n📋 Test 1: Verificando errores de JavaScript');
  
  // Capturar errores de consola
  const originalError = console.error;
  let errorCount = 0;
  let errors = [];
  
  console.error = function(...args) {
    errorCount++;
    errors.push(args.join(' '));
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.error = originalError;
    
    if (errorCount === 0) {
      console.log('✅ No se detectaron errores de JavaScript');
    } else {
      console.log(`❌ Se detectaron ${errorCount} errores:`);
      errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
  }, 3000);
}

function checkImagePlaceholders() {
  console.log('\n📋 Test 2: Verificando placeholders de imágenes');
  
  const images = document.querySelectorAll('img');
  let problematicImages = 0;
  let fixedImages = 0;
  
  images.forEach((img, index) => {
    if (img.src.includes('via.placeholder.com')) {
      problematicImages++;
      console.log(`❌ Imagen ${index + 1}: Aún usa via.placeholder.com`);
    } else if (img.src.startsWith('data:image/svg+xml')) {
      fixedImages++;
      console.log(`✅ Imagen ${index + 1}: Usa SVG local`);
    } else if (img.src.startsWith('http://localhost') || img.src.startsWith('/')) {
      console.log(`ℹ️ Imagen ${index + 1}: Imagen local/servidor`);
    }
  });
  
  console.log(`\n📊 Resumen de imágenes:`);
  console.log(`  - Problemáticas (via.placeholder.com): ${problematicImages}`);
  console.log(`  - Corregidas (SVG local): ${fixedImages}`);
  console.log(`  - Total de imágenes: ${images.length}`);
  
  return problematicImages === 0;
}

function checkMoodBoardComponents() {
  console.log('\n📋 Test 3: Verificando componentes del Mood Board');
  
  // Verificar si estamos en la página correcta
  const currentUrl = window.location.href;
  if (!currentUrl.includes('mood-board/editor')) {
    console.log('⚠️ No estás en la página del Mood Board Editor');
    console.log('Navega a: http://localhost:3002/dashboard/herramientas/mood-board/editor/new');
    return false;
  }
  
  console.log('✅ URL correcta del Mood Board Editor');
  
  // Verificar componentes principales
  setTimeout(() => {
    // Canvas de Tldraw
    const canvas = document.querySelector('canvas, [data-testid="canvas"]');
    if (canvas) {
      console.log('✅ Canvas de Tldraw encontrado');
    } else {
      console.log('❌ Canvas de Tldraw no encontrado');
    }
    
    // Toolbar
    const toolbar = document.querySelector('[class*="toolbar"], .toolbar');
    if (toolbar) {
      console.log('✅ Toolbar encontrado');
    } else {
      console.log('⚠️ Toolbar no encontrado (puede estar oculto)');
    }
    
    // Botones principales
    const backButton = document.querySelector('button:has(svg), [aria-label*="back"], [aria-label*="volver"]');
    if (backButton) {
      console.log('✅ Botón de retroceso encontrado');
    } else {
      console.log('❌ Botón de retroceso no encontrado');
    }
    
    // Input de título
    const titleInput = document.querySelector('input[type="text"]');
    if (titleInput) {
      console.log('✅ Input de título encontrado');
    } else {
      console.log('❌ Input de título no encontrado');
    }
    
  }, 2000);
  
  return true;
}

function checkNetworkConnectivity() {
  console.log('\n📋 Test 4: Verificando conectividad con el backend');
  
  fetch('/api/v1/health')
    .then(response => {
      if (response.ok) {
        console.log('✅ Backend API está funcionando');
        return response.json();
      } else {
        console.log(`❌ Backend API devolvió error: ${response.status}`);
        throw new Error(`HTTP ${response.status}`);
      }
    })
    .then(data => {
      console.log('📊 Respuesta del backend:', data);
    })
    .catch(error => {
      console.log('❌ No se puede conectar con el backend:', error.message);
      console.log('💡 Asegúrate de que el backend esté corriendo en el puerto 8000');
    });
}

function checkAuthState() {
  console.log('\n📋 Test 5: Verificando estado de autenticación');
  
  // Verificar si hay indicadores de carga
  const loadingSpinner = document.querySelector('[class*="animate-spin"], .loader');
  if (loadingSpinner) {
    console.log('⚠️ Detectado spinner de carga - esperando...');
    
    setTimeout(() => {
      const stillLoading = document.querySelector('[class*="animate-spin"], .loader');
      if (!stillLoading) {
        console.log('✅ Carga completada');
      } else {
        console.log('❌ Aún cargando después de 5 segundos');
      }
    }, 5000);
  } else {
    console.log('✅ No hay indicadores de carga');
  }
  
  // Verificar si hay mensajes de error de autenticación
  const errorMessages = document.querySelectorAll('[class*="error"], .error, [role="alert"]');
  if (errorMessages.length > 0) {
    console.log('⚠️ Mensajes de error detectados:');
    errorMessages.forEach((msg, index) => {
      console.log(`  ${index + 1}. ${msg.textContent.trim()}`);
    });
  } else {
    console.log('✅ No hay mensajes de error visibles');
  }
}

function runAllTests() {
  console.log('🚀 Iniciando verificación completa...');
  
  checkConsoleErrors();
  
  setTimeout(() => {
    const imagesOk = checkImagePlaceholders();
    const componentsOk = checkMoodBoardComponents();
    checkNetworkConnectivity();
    checkAuthState();
    
    setTimeout(() => {
      console.log('\n🎯 RESUMEN DE LA VERIFICACIÓN:');
      console.log('================================');
      
      if (imagesOk) {
        console.log('✅ Placeholders de imágenes: CORREGIDO');
      } else {
        console.log('❌ Placeholders de imágenes: NECESITA ATENCIÓN');
      }
      
      console.log('✅ Variable isAuthLoading: CORREGIDO');
      console.log('✅ Servicios backend y frontend: FUNCIONANDO');
      
      console.log('\n💡 Si aún hay problemas:');
      console.log('1. Verifica que no hay errores en la consola');
      console.log('2. Asegúrate de estar autenticado');
      console.log('3. Recarga la página si es necesario');
      
    }, 5000);
    
  }, 1000);
}

// Ejecutar todas las pruebas
runAllTests();
