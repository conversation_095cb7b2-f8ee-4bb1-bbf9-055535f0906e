/**
 * Test Authentication & Permission Fix
 * This script tests that the 403 permission errors are resolved
 * 
 * Usage: Open browser console on Visual Complexity Analyzer page and run:
 * copy and paste this entire script, then it will run automatically
 */

(async function testAuthFix() {
  console.log('🔐 TESTING AUTHENTICATION & PERMISSION FIX');
  console.log('='.repeat(60));
  
  try {
    // Step 1: Import modules
    console.log('\n📦 Step 1: Importing modules...');
    const { supabase } = await import('/src/lib/supabase.ts');
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    console.log('✅ Modules imported successfully');
    
    // Step 2: Check authentication
    console.log('\n🔐 Step 2: Authentication check...');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.log('❌ Authentication failed:', authError?.message || 'No user/session found');
      console.log('💡 Please log in and try again');
      return;
    }
    
    console.log('✅ User authenticated:', {
      userId: user.id,
      email: user.email,
      hasAccessToken: !!session.access_token,
      tokenType: session.token_type
    });
    
    // Step 3: Test direct database queries (the ones that were failing with 403)
    console.log('\n📊 Step 3: Testing database queries that were failing...');
    
    // Test 1: SELECT query (was failing with 403)
    console.log('\n🔍 Test 3.1: SELECT user analyses...');
    try {
      const { data: selectData, error: selectError } = await supabase
        .from('design_analyses')
        .select('overall_score, tool_type, is_favorite, created_at')
        .eq('user_id', user.id)
        .limit(5);
      
      if (selectError) {
        console.log('❌ SELECT query failed:', selectError.message);
        console.log('🔍 Error details:', selectError);
      } else {
        console.log('✅ SELECT query successful!');
        console.log('📊 Found', selectData.length, 'analyses');
      }
    } catch (selectException) {
      console.log('❌ SELECT query threw exception:', selectException.message);
    }
    
    // Test 2: INSERT query (was failing with 403)
    console.log('\n📝 Test 3.2: INSERT test analysis...');
    try {
      const testAnalysisData = {
        user_id: user.id,
        original_filename: 'auth-test.png',
        file_size: 1024,
        file_type: 'image/png',
        tool_type: 'visual-complexity-analyzer',
        overall_score: 75,
        complexity_scores: { color: 7, layout: 8, typography: 7 },
        analysis_areas: [
          { name: 'Test Area', score: 75, description: 'Auth test', recommendations: [] }
        ],
        recommendations: ['Test recommendation'],
        ai_analysis_summary: 'Authentication test analysis',
        status: 'completed',
        tags: ['auth-test']
      };
      
      const { data: insertData, error: insertError } = await supabase
        .from('design_analyses')
        .insert(testAnalysisData)
        .select()
        .single();
      
      if (insertError) {
        console.log('❌ INSERT query failed:', insertError.message);
        console.log('🔍 Error details:', insertError);
        
        // Analyze the specific error
        if (insertError.message.includes('permission') || insertError.code === '42501') {
          console.log('💡 This is still a permission error - RLS policies may need adjustment');
        } else if (insertError.message.includes('violates')) {
          console.log('💡 This is a constraint violation - check required fields');
        } else {
          console.log('💡 This is a different type of error');
        }
      } else {
        console.log('✅ INSERT query successful!');
        console.log('📊 Created analysis:', insertData.id);
        
        // Test 3: UPDATE query
        console.log('\n✏️ Test 3.3: UPDATE test analysis...');
        try {
          const { data: updateData, error: updateError } = await supabase
            .from('design_analyses')
            .update({ is_favorite: true })
            .eq('id', insertData.id)
            .select()
            .single();
          
          if (updateError) {
            console.log('❌ UPDATE query failed:', updateError.message);
          } else {
            console.log('✅ UPDATE query successful!');
          }
        } catch (updateException) {
          console.log('❌ UPDATE query threw exception:', updateException.message);
        }
        
        // Test 4: DELETE query
        console.log('\n🗑️ Test 3.4: DELETE test analysis...');
        try {
          const { error: deleteError } = await supabase
            .from('design_analyses')
            .delete()
            .eq('id', insertData.id);
          
          if (deleteError) {
            console.log('❌ DELETE query failed:', deleteError.message);
          } else {
            console.log('✅ DELETE query successful!');
          }
        } catch (deleteException) {
          console.log('❌ DELETE query threw exception:', deleteException.message);
        }
      }
    } catch (insertException) {
      console.log('❌ INSERT query threw exception:', insertException.message);
    }
    
    // Step 4: Test service methods (high-level API)
    console.log('\n🛠️ Step 4: Testing service methods...');
    
    // Test getUserAnalyses (was failing with 403)
    console.log('\n📋 Test 4.1: getUserAnalyses...');
    try {
      const analyses = await designAnalysisService.getUserAnalyses(user.id, { limit: 5 });
      console.log('✅ getUserAnalyses successful!');
      console.log('📊 Found', analyses.length, 'analyses');
    } catch (serviceError) {
      console.log('❌ getUserAnalyses failed:', serviceError.message);
    }
    
    // Test getUserStats
    console.log('\n📈 Test 4.2: getUserStats...');
    try {
      const stats = await designAnalysisService.getUserStats(user.id);
      console.log('✅ getUserStats successful!');
      console.log('📊 Stats:', {
        totalAnalyses: stats.totalAnalyses,
        favoriteAnalyses: stats.favoriteAnalyses,
        averageScore: stats.averageScore
      });
    } catch (statsError) {
      console.log('❌ getUserStats failed:', statsError.message);
    }
    
    // Step 5: Test complete saveAnalysis flow
    console.log('\n💾 Step 5: Testing complete saveAnalysis flow...');
    
    // Create a test image file
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#0000FF'; // Blue pixel
      ctx.fillRect(0, 0, 1, 1);
    }

    const testImageBlob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });

    const testFile = new File([testImageBlob], 'auth-fix-test.png', {
      type: 'image/png'
    });
    
    const testAnalysisData = {
      user_id: user.id,
      original_filename: testFile.name,
      file_size: testFile.size,
      file_type: testFile.type,
      overall_score: 90,
      complexity_scores: { 
        color: 9, 
        layout: 9, 
        typography: 8,
        hierarchy: 9,
        composition: 9,
        contrast: 8
      },
      analysis_areas: [
        { 
          name: 'Auth Fix Test', 
          score: 90, 
          description: 'Testing authentication fix', 
          recommendations: ['Auth fix verification'] 
        }
      ],
      recommendations: ['Authentication and permissions are now working'],
      ai_analysis_summary: 'Complete auth fix test - upload and database save',
      tags: ['auth-fix', 'complete-test']
    };
    
    try {
      console.log('💾 Calling saveAnalysis with auth fix...');
      const savedAnalysis = await designAnalysisService.saveAnalysis(testAnalysisData, testFile);
      
      console.log('🎉 COMPLETE SUCCESS! saveAnalysis worked!');
      console.log('📊 Result:', {
        id: savedAnalysis.id,
        hasFileUrl: !!savedAnalysis.file_url,
        originalFilename: savedAnalysis.original_filename,
        overallScore: savedAnalysis.overall_score
      });
      
      if (savedAnalysis.file_url) {
        console.log('✅ Image upload: Working');
        console.log('✅ Database save: Working');
        console.log('✅ Authentication: Working');
        console.log('✅ Permissions: Working');
      }
      
      // Clean up test analysis
      try {
        await designAnalysisService.deleteAnalysis(savedAnalysis.id);
        console.log('🧹 Test analysis cleaned up');
      } catch (deleteError) {
        console.log('⚠️ Could not clean up test analysis:', deleteError.message);
      }
      
    } catch (saveAnalysisError) {
      console.log('❌ saveAnalysis failed:', saveAnalysisError.message);
      
      if (saveAnalysisError.message.includes('permission') || 
          saveAnalysisError.message.includes('403')) {
        console.log('💡 Still have permission issues - need to investigate further');
      } else {
        console.log('💡 Different error - authentication might be working but other issue exists');
      }
    }
    
    // Final Summary
    console.log('\n📊 AUTHENTICATION FIX TEST SUMMARY');
    console.log('='.repeat(50));
    console.log('🔐 Authentication: ✅ Working');
    console.log('📊 Database Schema: Using public schema (should fix 403 errors)');
    console.log('🛠️ Service Methods: Testing completed');
    
    console.log('\n💡 EXPECTED RESULTS:');
    console.log('✅ If all tests pass: 403 errors are fixed!');
    console.log('❌ If tests still fail: Need to check RLS policies or user_id format');
    console.log('🔧 If partial success: Some operations work, others need investigation');
    
  } catch (error) {
    console.log('💥 Test failed with error:', error.message);
    console.log('🔍 Full error:', error);
  }
})();

console.log('🎯 Authentication Fix Test loaded and running...');
