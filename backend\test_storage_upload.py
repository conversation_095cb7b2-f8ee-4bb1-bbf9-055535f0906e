#!/usr/bin/env python3
"""
Test script to verify Supabase Storage upload functionality in Python
"""

import asyncio
import logging
from app.core.supabase import SupabaseService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_storage_upload():
    """Test the storage upload functionality"""
    
    try:
        # Create service instance
        service = SupabaseService()
        
        # Create test image content
        test_content = b"Test image content for storage upload"
        
        # Test parameters
        user_id = "test-user-123"
        filename = "test-upload.png"
        content_type = "image/png"
        
        logger.info("🧪 Testing Supabase Storage upload...")
        logger.info(f"📊 Test parameters: user_id={user_id}, filename={filename}, size={len(test_content)}")
        
        # Attempt upload
        result = await service.upload_image_to_storage(
            user_id=user_id,
            image_content=test_content,
            original_filename=filename,
            content_type=content_type
        )
        
        if result:
            logger.info(f"✅ Upload successful: {result}")
            
            # Test cleanup
            logger.info("🧹 Testing cleanup...")
            cleanup_result = await service.delete_image_from_storage(result)
            if cleanup_result:
                logger.info("✅ Cleanup successful")
            else:
                logger.warning("⚠️ Cleanup failed")
                
        else:
            logger.error("❌ Upload failed")
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        logger.error(f"🔍 Exception type: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(test_storage_upload())
