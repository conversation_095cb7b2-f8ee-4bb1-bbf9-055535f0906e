/**
 * Test New Analysis Flow
 * Creates a new analysis and verifies the complete end-to-end image storage and retrieval flow
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🧪 Starting new analysis flow test...');

async function testNewAnalysisFlow() {
  try {
    // Check if required services are available
    if (typeof designAnalysisService === 'undefined') {
      console.error('❌ designAnalysisService not available. Make sure you are on the Visual Complexity Analyzer page.');
      return;
    }

    console.log('✅ designAnalysisService is available');

    // Create a simple test image (1x1 pixel PNG)
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    
    // Draw a simple test pattern
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 50, 50);
    ctx.fillStyle = '#00FF00';
    ctx.fillRect(50, 0, 50, 50);
    ctx.fillStyle = '#0000FF';
    ctx.fillRect(0, 50, 50, 50);
    ctx.fillStyle = '#FFFF00';
    ctx.fillRect(50, 50, 50, 50);

    // Convert canvas to blob
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });

    console.log('✅ Test image created:', {
      size: blob.size,
      type: blob.type
    });

    // Create a File object
    const testFile = new File([blob], 'test-image-flow.png', { type: 'image/png' });
    console.log('✅ Test file created:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });

    // Test the complete analysis flow
    console.log('🔄 Starting analysis...');
    
    // Mock analysis data (you would normally get this from the actual analysis)
    const mockAnalysisData = {
      overall_score: 75,
      complexity_scores: {
        color: 80,
        layout: 70,
        typography: 75,
        elements: 85,
        hierarchy: 65,
        composition: 80,
        contrast: 90,
        whitespace: 60
      },
      analysis_areas: [
        {
          name: "Color Complexity",
          score: 80,
          description: "Test color analysis"
        }
      ],
      recommendations: [
        {
          category: "Color",
          priority: "medium",
          suggestion: "Test recommendation"
        }
      ],
      ai_analysis_summary: "Test analysis summary for flow verification",
      gemini_analysis: "Test Gemini analysis",
      agent_message: "Test agent message"
    };

    try {
      // Save the analysis (this should include image upload to Supabase Storage)
      console.log('💾 Saving analysis with image...');
      const savedAnalysis = await designAnalysisService.saveAnalysis({
        file: testFile,
        analysisData: mockAnalysisData
      });

      console.log('✅ Analysis saved successfully:', {
        id: savedAnalysis.id,
        filename: savedAnalysis.original_filename,
        file_url: savedAnalysis.file_url,
        hasFileUrl: !!savedAnalysis.file_url
      });

      if (!savedAnalysis.file_url) {
        console.error('❌ CRITICAL: Saved analysis has no file_url! Backend image storage may not be working.');
        return;
      }

      // Test image retrieval
      console.log('🔍 Testing image retrieval for new analysis...');
      const retrievedUrl = await designAnalysisService.getImageUrl(savedAnalysis.file_url);
      
      if (retrievedUrl) {
        console.log('✅ Image retrieval SUCCESS:', {
          type: retrievedUrl.startsWith('blob:') ? 'Blob URL' : retrievedUrl.startsWith('http') ? 'HTTP URL' : 'Unknown',
          url: retrievedUrl.substring(0, 50) + '...'
        });

        // Test if the image actually loads
        const imageLoadTest = await testImageLoad(retrievedUrl);
        console.log(`🖼️ Image load test: ${imageLoadTest ? '✅ SUCCESS' : '❌ FAILED'}`);

        if (imageLoadTest) {
          console.log('🎉 COMPLETE SUCCESS: New analysis flow working perfectly!');
          console.log('✅ Image was uploaded to Supabase Storage');
          console.log('✅ file_url was saved to database');
          console.log('✅ Image can be retrieved and displayed');
        } else {
          console.error('❌ Image retrieval worked but image failed to load');
        }

        // Clean up blob URL
        if (retrievedUrl.startsWith('blob:')) {
          URL.revokeObjectURL(retrievedUrl);
        }
      } else {
        console.error('❌ Image retrieval FAILED for new analysis');
      }

      // Test loading the analysis (simulate the load flow)
      console.log('🔄 Testing analysis load flow...');
      const loadedAnalyses = await designAnalysisService.getUserAnalyses({ limit: 1 });
      
      if (loadedAnalyses.length > 0 && loadedAnalyses[0].id === savedAnalysis.id) {
        const loadedAnalysis = loadedAnalyses[0];
        console.log('✅ Analysis loaded from database:', {
          id: loadedAnalysis.id,
          file_url: loadedAnalysis.file_url,
          hasFileUrl: !!loadedAnalysis.file_url
        });

        if (loadedAnalysis.file_url) {
          console.log('🎉 PERFECT: Loaded analysis has file_url - frontend should display image correctly!');
        } else {
          console.error('❌ PROBLEM: Loaded analysis missing file_url');
        }
      }

    } catch (error) {
      console.error('💥 Analysis save/test ERROR:', error);
    }

  } catch (error) {
    console.error('💥 Test setup ERROR:', error);
  }
}

// Helper function to test if an image URL actually loads
function testImageLoad(url) {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
    
    // Timeout after 10 seconds
    setTimeout(() => resolve(false), 10000);
  });
}

// Run the test
testNewAnalysisFlow();
