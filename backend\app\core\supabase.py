"""
Supabase client configuration for Emma Studio backend
"""

import logging
import hashlib
import time
from typing import Optional, Dict, Any, List
from supabase import create_client, Client

logger = logging.getLogger(__name__)

# Supabase configuration - using the same values as frontend for consistency
SUPABASE_URL = "https://pthewpjbegkgomvyhkin.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k"

# Create Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

logger.info(f"Supabase client initialized for URL: {SUPABASE_URL}")


class SupabaseService:
    """Service class for Supabase database operations"""
    
    def __init__(self):
        self.client = supabase
    
    async def save_design_analysis(
        self,
        user_id: str,
        analysis_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Save a design analysis to the database
        
        Args:
            user_id: The ID of the user who owns this analysis
            analysis_data: The analysis data to save
            
        Returns:
            The saved analysis record or None if failed
        """
        try:
            # Prepare the data for insertion
            insert_data = {
                "user_id": user_id,
                "original_filename": analysis_data.get("original_filename", "unknown.jpg"),
                "file_size": analysis_data.get("file_size"),
                "file_type": analysis_data.get("file_type"),
                "file_url": analysis_data.get("file_url"),
                "tool_type": "visual_complexity_analyzer",
                "analysis_version": "1.0",
                "overall_score": analysis_data.get("score", 0),
                "complexity_scores": analysis_data.get("complexity", {}),
                "analysis_areas": analysis_data.get("areas", []),
                "recommendations": analysis_data.get("recommendations", []),
                "ai_analysis_summary": analysis_data.get("analysis_summary"),
                "gemini_analysis": analysis_data.get("gemini_analysis"),
                "agent_message": analysis_data.get("agent_message"),
                "visuai_insights": analysis_data.get("visuai_insights"),
                "analysis_duration_ms": analysis_data.get("analysis_duration_ms"),
                "status": "completed",
                "is_favorite": False,
                "tags": []
            }
            
            # Insert into database
            result = self.client.schema("api").table("design_analyses").insert(insert_data).execute()
            
            if result.data:
                logger.info(f"Successfully saved design analysis for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save design analysis: {result}")
                return None
                
        except Exception as e:
            logger.error(f"Error saving design analysis: {str(e)}")
            return None
    
    async def save_design_upload(
        self,
        user_id: str,
        filename: str,
        file_size: int,
        file_type: str,
        file_url: str,
        file_hash: Optional[str] = None,
        analysis_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Save information about an uploaded design file
        
        Args:
            user_id: The ID of the user who uploaded the file
            filename: Original filename
            file_size: Size of the file in bytes
            file_type: MIME type of the file
            file_url: URL where the file is stored
            file_hash: Optional hash of the file content
            analysis_id: Optional ID of the related analysis
            
        Returns:
            The saved upload record or None if failed
        """
        try:
            insert_data = {
                "user_id": user_id,
                "original_filename": filename,
                "file_size": file_size,
                "file_type": file_type,
                "file_url": file_url,
                "file_hash": file_hash,
                "upload_source": "design_complexity_analyzer",
                "is_processed": analysis_id is not None,
                "analysis_id": analysis_id
            }
            
            result = self.client.schema("api").table("design_uploads").insert(insert_data).execute()
            
            if result.data:
                logger.info(f"Successfully saved design upload for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save design upload: {result}")
                return None
                
        except Exception as e:
            logger.error(f"Error saving design upload: {str(e)}")
            return None

    async def upload_image_to_storage(
        self,
        user_id: str,
        image_content: bytes,
        original_filename: str,
        content_type: str,
        user_jwt_token: Optional[str] = None
    ) -> Optional[str]:
        """
        Upload an image to Supabase Storage

        Args:
            user_id: The ID of the user uploading the image
            image_content: The image file content as bytes
            original_filename: Original filename of the image
            content_type: MIME type of the image
            user_jwt_token: Optional JWT token for user authentication

        Returns:
            The file path in storage if successful, None if failed
        """
        try:
            # Generate a unique filename to avoid conflicts
            timestamp = int(time.time() * 1000)  # milliseconds
            file_hash = hashlib.md5(image_content).hexdigest()[:8]

            # ✅ FIXED: Preserve international characters while making filename storage-safe
            # Replace problematic characters but preserve Unicode letters and numbers
            import re
            import unicodedata

            # Normalize Unicode characters (NFD form) to handle accented characters properly
            normalized_filename = unicodedata.normalize('NFD', original_filename)

            # Keep Unicode letters, numbers, dots, hyphens, and underscores
            # Replace other characters with underscores
            safe_filename = re.sub(r'[^\w\.\-]', '_', normalized_filename, flags=re.UNICODE)

            # Remove multiple consecutive underscores and trim
            safe_filename = re.sub(r'_+', '_', safe_filename).strip('_')

            if not safe_filename:
                safe_filename = "image"

            logger.info(f"Filename normalization: '{original_filename}' -> '{safe_filename}'")

            # Create the storage path: user_id/timestamp_hash_filename
            file_path = f"{user_id}/{timestamp}_{file_hash}_{safe_filename}"

            logger.info(f"Uploading image to storage: {file_path}")

            # Create authenticated client if JWT token is provided
            client_to_use = self.client
            if user_jwt_token:
                logger.info("🔐 Using authenticated client for storage upload")
                # Create a new client instance with the user's JWT token
                from supabase import create_client
                client_to_use = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

                # Set the JWT token for authentication
                # For Python supabase client, we need to set the auth token directly
                try:
                    # Method 1: Try setting the session with access token
                    client_to_use.auth.set_session(user_jwt_token, None)
                    logger.info("✅ Successfully set authenticated session (method 1)")

                except Exception as auth_error1:
                    logger.warning(f"⚠️ Method 1 failed: {auth_error1}")

                    try:
                        # Method 2: Try setting the session with empty refresh token
                        client_to_use.auth.set_session(user_jwt_token, "")
                        logger.info("✅ Successfully set authenticated session (method 2)")

                    except Exception as auth_error2:
                        logger.warning(f"⚠️ Method 2 failed: {auth_error2}")

                        try:
                            # Method 3: Try setting headers manually
                            client_to_use.auth._headers = {"Authorization": f"Bearer {user_jwt_token}"}
                            logger.info("✅ Successfully set authenticated session (method 3)")

                        except Exception as auth_error3:
                            logger.warning(f"⚠️ Method 3 failed: {auth_error3}")
                            logger.info("🔄 All auth methods failed, falling back to default client")
                            client_to_use = self.client

            # Upload to Supabase Storage
            # Using correct Python supabase client API
            result = client_to_use.storage.from_("design-analysis-images").upload(
                file=image_content,
                path=file_path,
                file_options={
                    "content-type": content_type,
                    "cache-control": "3600",
                    "upsert": "false"  # ✅ Fixed: should be string, not boolean
                }
            )

            # Check if upload was successful
            # Python supabase client returns different response structure
            if hasattr(result, 'data') and result.data:
                logger.info(f"✅ Successfully uploaded image to storage: {file_path}")
                logger.info(f"📊 Upload details: size={len(image_content)} bytes, type={content_type}")
                return file_path
            elif hasattr(result, 'path') or not hasattr(result, 'error'):
                # If no error attribute or has path, assume success
                logger.info(f"✅ Successfully uploaded image to storage: {file_path}")
                logger.info(f"📊 Upload details: size={len(image_content)} bytes, type={content_type}")
                logger.info(f"🔍 Upload result type: {type(result)}")
                return file_path
            else:
                logger.error(f"❌ Failed to upload image to storage")
                logger.error(f"🔍 Upload result: {result}")
                logger.error(f"🔍 Upload result type: {type(result)}")
                if hasattr(result, 'error') and result.error:
                    logger.error(f"🔍 Upload error details: {result.error}")
                return None

        except Exception as e:
            logger.error(f"❌ Exception during image upload to storage: {str(e)}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")
            logger.error(f"🔍 Upload parameters: user_id={user_id}, filename={original_filename}, size={len(image_content)}, type={content_type}")

            # Log specific error types for better debugging
            if "permission" in str(e).lower() or "policy" in str(e).lower():
                logger.error("🔒 This appears to be a permissions/RLS policy error")
            elif "duplicate" in str(e).lower():
                logger.error("📁 This appears to be a duplicate file error")
            elif "size" in str(e).lower() or "limit" in str(e).lower():
                logger.error("📏 This appears to be a file size limit error")

            return None

    async def delete_image_from_storage(self, file_path: str) -> bool:
        """
        Delete an image from Supabase Storage

        Args:
            file_path: The file path in storage to delete

        Returns:
            True if successful, False if failed
        """
        try:
            result = self.client.storage.from_("design-analysis-images").remove([file_path])

            if result.data:
                logger.info(f"Successfully deleted image from storage: {file_path}")
                return True
            else:
                logger.error(f"Failed to delete image from storage: {result}")
                return False

        except Exception as e:
            logger.error(f"Error deleting image from storage: {str(e)}")
            return False
    
    async def get_user_analyses(
        self,
        user_id: str,
        limit: int = 50,
        tool_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get design analyses for a specific user
        
        Args:
            user_id: The ID of the user
            limit: Maximum number of records to return
            tool_type: Optional filter by tool type
            
        Returns:
            List of analysis records
        """
        try:
            query = self.client.schema("api").table("design_analyses").select("*").eq("user_id", user_id)
            
            if tool_type:
                query = query.eq("tool_type", tool_type)
            
            query = query.order("created_at", desc=True).limit(limit)
            
            result = query.execute()
            
            if result.data:
                logger.info(f"Retrieved {len(result.data)} analyses for user {user_id}")
                return result.data
            else:
                logger.info(f"No analyses found for user {user_id}")
                return []
                
        except Exception as e:
            logger.error(f"Error retrieving user analyses: {str(e)}")
            return []
    
    async def get_analysis_by_id(
        self,
        analysis_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific analysis by ID (with user verification for security)
        
        Args:
            analysis_id: The ID of the analysis
            user_id: The ID of the user (for security check)
            
        Returns:
            The analysis record or None if not found/not authorized
        """
        try:
            result = self.client.schema("api").table("design_analyses").select("*").eq("id", analysis_id).eq("user_id", user_id).execute()
            
            if result.data and len(result.data) > 0:
                logger.info(f"Retrieved analysis {analysis_id} for user {user_id}")
                return result.data[0]
            else:
                logger.warning(f"Analysis {analysis_id} not found or not authorized for user {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving analysis: {str(e)}")
            return None
    
    async def update_analysis_favorite(
        self,
        analysis_id: str,
        user_id: str,
        is_favorite: bool
    ) -> bool:
        """
        Update the favorite status of an analysis
        
        Args:
            analysis_id: The ID of the analysis
            user_id: The ID of the user (for security check)
            is_favorite: New favorite status
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.client.schema("api").table("design_analyses").update({
                "is_favorite": is_favorite
            }).eq("id", analysis_id).eq("user_id", user_id).execute()
            
            if result.data:
                logger.info(f"Updated favorite status for analysis {analysis_id}")
                return True
            else:
                logger.error(f"Failed to update favorite status for analysis {analysis_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating favorite status: {str(e)}")
            return False
    
    async def delete_analysis(
        self,
        analysis_id: str,
        user_id: str
    ) -> bool:
        """
        Delete an analysis (with user verification for security)
        
        Args:
            analysis_id: The ID of the analysis
            user_id: The ID of the user (for security check)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.client.schema("api").table("design_analyses").delete().eq("id", analysis_id).eq("user_id", user_id).execute()
            
            if result.data:
                logger.info(f"Deleted analysis {analysis_id} for user {user_id}")
                return True
            else:
                logger.error(f"Failed to delete analysis {analysis_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting analysis: {str(e)}")
            return False

    # ===============================================
    # MOODBOARD OPERATIONS
    # ===============================================

    async def save_moodboard(
        self,
        user_id: str,
        moodboard_data: Dict[str, Any],
        user_jwt_token: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Save a moodboard to the database

        Args:
            user_id: The ID of the user
            moodboard_data: Dictionary containing moodboard information

        Returns:
            The saved moodboard record or None if failed
        """
        try:
            # Use authenticated client if JWT token is provided
            client_to_use = self.client
            if user_jwt_token:
                try:
                    # Create a new client instance with the user's JWT token
                    from supabase import create_client
                    client_to_use = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
                    # Set the session with the JWT token - this is the correct method
                    client_to_use.auth.set_session(user_jwt_token, "")  # Empty refresh token for server-side usage
                    logger.info(f"✅ Using authenticated Supabase client for creating mood board for user {user_id}")
                except Exception as auth_error:
                    logger.warning(f"⚠️ Failed to set JWT token for creation, using default client: {auth_error}")
                    client_to_use = self.client

            # Prepare the data for insertion
            insert_data = {
                "user_id": user_id,
                "title": moodboard_data.get("title", "Untitled Moodboard"),
                "description": moodboard_data.get("description"),
                "tool_type": "interactive_moodboard",
                "version": "1.0",
                "tldraw_data": moodboard_data.get("tldraw_data"),
                "canvas_snapshot": moodboard_data.get("canvas_snapshot"),
                "tags": moodboard_data.get("tags", []),
                "is_public": moodboard_data.get("is_public", False),
                "is_favorite": moodboard_data.get("is_favorite", False),
                "collaboration_enabled": moodboard_data.get("collaboration_enabled", False),
                "shared_with": moodboard_data.get("shared_with", []),
                "view_count": 0,
                "status": "active",
                "notes": moodboard_data.get("notes")
            }

            # Insert into database
            result = client_to_use.schema("api").table("moodboards").insert(insert_data).execute()

            if result.data:
                logger.info(f"Successfully saved moodboard for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save moodboard - no data returned: {result}")
                return None

        except Exception as e:
            logger.error(f"Error saving moodboard: {str(e)}")
            # Log additional details for debugging
            if hasattr(e, 'details'):
                logger.error(f"Error details: {e.details}")
            if hasattr(e, 'message'):
                logger.error(f"Error message: {e.message}")
            if hasattr(e, 'code'):
                logger.error(f"Error code: {e.code}")
            return None

    async def update_moodboard(
        self,
        moodboard_id: str,
        user_id: str,
        moodboard_data: Dict[str, Any],
        user_jwt_token: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing moodboard

        Args:
            moodboard_id: The ID of the moodboard to update
            user_id: The ID of the user (for security check)
            moodboard_data: Dictionary containing updated moodboard information
            user_jwt_token: JWT token for RLS authentication

        Returns:
            The updated moodboard record or None if failed
        """
        try:
            # Use authenticated client if JWT token is provided
            client_to_use = self.client
            if user_jwt_token:
                try:
                    # Create a new client instance with the user's JWT token
                    from supabase import create_client
                    client_to_use = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
                    # Set the session with the JWT token
                    client_to_use.auth.set_session(user_jwt_token, "")
                    logger.info(f"✅ Using authenticated Supabase client for updating mood board {moodboard_id} for user {user_id}")
                except Exception as auth_error:
                    logger.error(f"❌ Error setting up authenticated client for update: {str(auth_error)}")
                    return None
            # Prepare the data for update
            update_data = {}

            # Only update fields that are provided
            if "title" in moodboard_data:
                update_data["title"] = moodboard_data["title"]
            if "description" in moodboard_data:
                update_data["description"] = moodboard_data["description"]
            if "tldraw_data" in moodboard_data:
                update_data["tldraw_data"] = moodboard_data["tldraw_data"]
            if "canvas_snapshot" in moodboard_data:
                update_data["canvas_snapshot"] = moodboard_data["canvas_snapshot"]
            if "tags" in moodboard_data:
                update_data["tags"] = moodboard_data["tags"]
            if "is_public" in moodboard_data:
                update_data["is_public"] = moodboard_data["is_public"]
            if "is_favorite" in moodboard_data:
                update_data["is_favorite"] = moodboard_data["is_favorite"]
            if "collaboration_enabled" in moodboard_data:
                update_data["collaboration_enabled"] = moodboard_data["collaboration_enabled"]
            if "shared_with" in moodboard_data:
                update_data["shared_with"] = moodboard_data["shared_with"]
            if "status" in moodboard_data:
                update_data["status"] = moodboard_data["status"]
            if "notes" in moodboard_data:
                update_data["notes"] = moodboard_data["notes"]

            # Update in database using the appropriate client
            result = client_to_use.schema("api").table("moodboards").update(update_data).eq("id", moodboard_id).eq("user_id", user_id).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"Successfully updated moodboard {moodboard_id} for user {user_id}")
                return result.data[0]
            else:
                logger.warning(f"Moodboard {moodboard_id} not found or not authorized for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Error updating moodboard: {str(e)}")
            return None

    async def get_user_moodboards(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0,
        status: str = "active",
        user_jwt_token: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get all moodboards for a user

        Args:
            user_id: The ID of the user
            limit: Maximum number of records to return
            offset: Number of records to skip
            status: Filter by status (default: "active")
            user_jwt_token: JWT token for RLS authentication

        Returns:
            List of moodboard records
        """
        try:
            # Use authenticated client if JWT token is provided
            client_to_use = self.client
            if user_jwt_token:
                try:
                    # Create a new client instance with the user's JWT token
                    from supabase import create_client
                    client_to_use = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
                    # Set the session with the JWT token - this is the correct method
                    client_to_use.auth.set_session(user_jwt_token, "")  # Empty refresh token for server-side usage
                    logger.info(f"✅ Using authenticated Supabase client for user {user_id}")
                except Exception as auth_error:
                    logger.warning(f"⚠️ Failed to set JWT token, using default client: {auth_error}")
                    client_to_use = self.client

            query = client_to_use.schema("api").table("moodboards").select("*").eq("user_id", user_id)

            if status:
                query = query.eq("status", status)

            result = query.order("updated_at", desc=True).limit(limit).offset(offset).execute()

            if result.data:
                logger.info(f"Retrieved {len(result.data)} moodboards for user {user_id}")
                return result.data
            else:
                logger.info(f"No moodboards found for user {user_id}")
                return []

        except Exception as e:
            logger.error(f"Error getting user moodboards: {str(e)}")
            return []

    async def get_moodboard_by_id(
        self,
        moodboard_id: str,
        user_id: str,
        user_jwt_token: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific moodboard by ID (with user verification for security)

        Args:
            moodboard_id: The ID of the moodboard
            user_id: The ID of the user (for security check)
            user_jwt_token: Optional JWT token for authenticated access

        Returns:
            The moodboard record or None if not found/not authorized
        """
        try:
            # Use authenticated client if JWT token is provided
            client_to_use = self.client
            if user_jwt_token:
                try:
                    # Create a new client instance with the user's JWT token
                    from supabase import create_client
                    client_to_use = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
                    # Set the session with the JWT token - this is the correct method
                    client_to_use.auth.set_session(user_jwt_token, "")  # Empty refresh token for server-side usage
                    logger.info(f"✅ Using authenticated Supabase client for getting mood board {moodboard_id} for user {user_id}")
                except Exception as auth_error:
                    logger.warning(f"⚠️ Failed to set JWT token, using default client: {auth_error}")
                    client_to_use = self.client

            result = client_to_use.schema("api").table("moodboards").select("*").eq("id", moodboard_id).eq("user_id", user_id).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"Retrieved moodboard {moodboard_id} for user {user_id}")
                # Update view count and last viewed timestamp
                await self._update_moodboard_view_stats(moodboard_id, user_id)
                return result.data[0]
            else:
                logger.warning(f"Moodboard {moodboard_id} not found or not authorized for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting moodboard by ID: {str(e)}")
            return None

    async def delete_moodboard(
        self,
        moodboard_id: str,
        user_id: str,
        user_jwt_token: Optional[str] = None
    ) -> bool:
        """
        Delete a moodboard (soft delete by updating status)

        Args:
            moodboard_id: The ID of the moodboard to delete
            user_id: The ID of the user (for security check)
            user_jwt_token: JWT token for RLS authentication

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use authenticated client if JWT token is provided
            client_to_use = self.client
            if user_jwt_token:
                try:
                    # Create a new client instance with the user's JWT token
                    from supabase import create_client
                    client_to_use = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
                    # Set the session with the JWT token - this is the correct method
                    client_to_use.auth.set_session(user_jwt_token, "")  # Empty refresh token for server-side usage
                    logger.info(f"✅ Using authenticated Supabase client for deleting mood board {moodboard_id} for user {user_id}")
                except Exception as auth_error:
                    logger.warning(f"⚠️ Failed to set JWT token for deletion, using default client: {auth_error}")
                    client_to_use = self.client

            result = client_to_use.schema("api").table("moodboards").update({"status": "deleted"}).eq("id", moodboard_id).eq("user_id", user_id).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"Successfully deleted moodboard {moodboard_id} for user {user_id}")
                return True
            else:
                logger.warning(f"Moodboard {moodboard_id} not found or not authorized for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting moodboard: {str(e)}")
            return False

    async def save_moodboard_history(
        self,
        moodboard_id: str,
        user_id: str,
        change_type: str,
        tldraw_data_snapshot: Dict[str, Any],
        change_description: str = None,
        is_auto_save: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Save a moodboard history entry

        Args:
            moodboard_id: The ID of the moodboard
            user_id: The ID of the user
            change_type: Type of change ('create', 'update', 'snapshot')
            tldraw_data_snapshot: Snapshot of the tldraw data
            change_description: Optional description of the change
            is_auto_save: Whether this is an auto-save or manual save

        Returns:
            The saved history record or None if failed
        """
        try:
            # Get the current version number
            version_result = self.client.schema("api").table("moodboard_history").select("version_number").eq("moodboard_id", moodboard_id).order("version_number", desc=True).limit(1).execute()

            next_version = 1
            if version_result.data and len(version_result.data) > 0:
                next_version = version_result.data[0]["version_number"] + 1

            # Prepare the data for insertion
            insert_data = {
                "moodboard_id": moodboard_id,
                "user_id": user_id,
                "change_type": change_type,
                "change_description": change_description,
                "tldraw_data_snapshot": tldraw_data_snapshot,
                "version_number": next_version,
                "is_auto_save": is_auto_save
            }

            # Insert into database
            result = self.client.schema("api").table("moodboard_history").insert(insert_data).execute()

            if result.data:
                logger.info(f"Successfully saved moodboard history for moodboard {moodboard_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save moodboard history: {result}")
                return None

        except Exception as e:
            logger.error(f"Error saving moodboard history: {str(e)}")
            return None

    async def _update_moodboard_view_stats(
        self,
        moodboard_id: str,
        user_id: str
    ) -> None:
        """
        Update view count and last viewed timestamp for a moodboard

        Args:
            moodboard_id: The ID of the moodboard
            user_id: The ID of the user
        """
        try:
            # Get current view count
            current_result = self.client.schema("api").table("moodboards").select("view_count").eq("id", moodboard_id).eq("user_id", user_id).execute()

            current_count = 0
            if current_result.data and len(current_result.data) > 0:
                current_count = current_result.data[0].get("view_count", 0)

            # Update view stats
            update_data = {
                "view_count": current_count + 1,
                "last_viewed_at": "NOW()"
            }

            self.client.schema("api").table("moodboards").update(update_data).eq("id", moodboard_id).eq("user_id", user_id).execute()

        except Exception as e:
            logger.error(f"Error updating moodboard view stats: {str(e)}")

    # ===============================================
    # USER PALETTE OPERATIONS
    # ===============================================

    async def save_user_palette(
        self,
        user_id: str,
        palette_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Save a user-defined color palette to the database

        Args:
            user_id: The ID of the user
            palette_data: Dictionary containing palette information

        Returns:
            The saved palette record or None if failed
        """
        try:
            # Prepare the data for insertion
            insert_data = {
                "user_id": user_id,
                "name": palette_data.get("name", "Untitled Palette"),
                "colors": palette_data.get("colors", []),
                "description": palette_data.get("description"),
                "tags": palette_data.get("tags", []),
                "is_favorite": palette_data.get("is_favorite", False)
            }

            # Insert into database
            result = self.client.schema("api").table("user_palettes").insert(insert_data).execute()

            if result.data:
                logger.info(f"Successfully saved user palette for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save user palette: {result}")
                return None

        except Exception as e:
            logger.error(f"Error saving user palette: {str(e)}")
            return None

    async def save_focus_group_simulation(
        self,
        user_id: str,
        simulation_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Save a focus group simulation to the database

        Args:
            user_id: The ID of the user who owns this simulation
            simulation_data: The simulation data to save

        Returns:
            The saved simulation record or None if failed
        """
        try:
            # Prepare the data for insertion
            insert_data = {
                "user_id": user_id,
                "content": simulation_data.get("content", ""),
                "product_category": simulation_data.get("product_category"),
                "context": simulation_data.get("context"),
                "custom_questions": simulation_data.get("custom_questions", []),
                "num_participants": simulation_data.get("num_participants", 6),
                "discussion_rounds": simulation_data.get("discussion_rounds", 3),
                "simulation_results": simulation_data.get("simulation_results", {}),
                "participants": simulation_data.get("participants", []),
                "discussions": simulation_data.get("discussions", []),
                "summary": simulation_data.get("summary", {}),
                "simulation_duration_ms": simulation_data.get("simulation_duration_ms"),
                "status": "completed",
                "is_favorite": False,
                "custom_name": simulation_data.get("custom_name"),
                "tags": simulation_data.get("tags", []),
                "notes": simulation_data.get("notes"),
                "view_count": 0,
                "regeneration_count": 0
            }

            # Insert into database
            result = self.client.schema("api").table("focus_group_simulations").insert(insert_data).execute()

            if result.data:
                logger.info(f"Successfully saved focus group simulation for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to save focus group simulation: {result}")
                return None

        except Exception as e:
            logger.error(f"Error saving focus group simulation: {str(e)}")
            return None

    async def get_user_focus_group_simulations(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0,
        is_favorite: Optional[bool] = None,
        order_by: str = "created_at",
        order_direction: str = "desc"
    ) -> List[Dict[str, Any]]:
        """
        Get focus group simulations for a user

        Args:
            user_id: The user ID to get simulations for
            limit: Maximum number of simulations to return
            offset: Number of simulations to skip
            is_favorite: Filter by favorite status (None for all)
            order_by: Field to order by
            order_direction: Order direction (asc/desc)

        Returns:
            List of simulation records
        """
        try:
            query = self.client.schema("api").table("focus_group_simulations").select("*").eq("user_id", user_id)

            # Apply favorite filter if specified
            if is_favorite is not None:
                query = query.eq("is_favorite", is_favorite)

            # Apply ordering
            ascending = order_direction.lower() == "asc"
            query = query.order(order_by, desc=not ascending)

            # Apply pagination
            query = query.range(offset, offset + limit - 1)

            result = query.execute()

            if result.data:
                logger.info(f"Retrieved {len(result.data)} focus group simulations for user {user_id}")
                return result.data
            else:
                logger.info(f"No focus group simulations found for user {user_id}")
                return []

        except Exception as e:
            logger.error(f"Error getting user focus group simulations: {str(e)}")
            return []

    async def get_recent_focus_group_simulations(
        self,
        user_id: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get the most recent focus group simulations for a user

        Args:
            user_id: The user ID to get simulations for
            limit: Maximum number of simulations to return (default: 5)

        Returns:
            List of recent simulation records
        """
        return await self.get_user_focus_group_simulations(
            user_id=user_id,
            limit=limit,
            order_by="created_at",
            order_direction="desc"
        )

    async def get_favorite_focus_group_simulations(
        self,
        user_id: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get favorite focus group simulations for a user

        Args:
            user_id: The user ID to get simulations for
            limit: Maximum number of simulations to return

        Returns:
            List of favorite simulation records
        """
        return await self.get_user_focus_group_simulations(
            user_id=user_id,
            limit=limit,
            is_favorite=True,
            order_by="updated_at",
            order_direction="desc"
        )

    async def get_focus_group_simulation_by_id(
        self,
        simulation_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific focus group simulation by ID

        Args:
            simulation_id: The simulation ID
            user_id: The user ID (for security)

        Returns:
            The simulation record or None if not found
        """
        try:
            result = self.client.schema("api").table("focus_group_simulations").select("*").eq("id", simulation_id).eq("user_id", user_id).single().execute()

            if result.data:
                logger.info(f"Retrieved focus group simulation {simulation_id} for user {user_id}")
                return result.data
            else:
                logger.warning(f"Focus group simulation {simulation_id} not found for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting focus group simulation by ID: {str(e)}")
            return None

    async def update_focus_group_simulation(
        self,
        simulation_id: str,
        user_id: str,
        update_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update a focus group simulation

        Args:
            simulation_id: The simulation ID to update
            user_id: The user ID (for security)
            update_data: The data to update

        Returns:
            The updated simulation record or None if failed
        """
        try:
            # Only allow certain fields to be updated
            allowed_fields = {
                "custom_name", "is_favorite", "tags", "notes",
                "view_count", "last_viewed_at", "regeneration_count"
            }

            filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields}

            if not filtered_data:
                logger.warning("No valid fields to update")
                return None

            result = self.client.schema("api").table("focus_group_simulations").update(filtered_data).eq("id", simulation_id).eq("user_id", user_id).select().single().execute()

            if result.data:
                logger.info(f"Successfully updated focus group simulation {simulation_id} for user {user_id}")
                return result.data
            else:
                logger.error(f"Failed to update focus group simulation {simulation_id}")
                return None

        except Exception as e:
            logger.error(f"Error updating focus group simulation: {str(e)}")
            return None

    async def toggle_focus_group_favorite(
        self,
        simulation_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Toggle the favorite status of a focus group simulation

        Args:
            simulation_id: The simulation ID
            user_id: The user ID (for security)

        Returns:
            The updated simulation record or None if failed
        """
        try:
            # First get the current favorite status
            current = await self.get_focus_group_simulation_by_id(simulation_id, user_id)
            if not current:
                return None

            new_favorite_status = not current.get("is_favorite", False)

            return await self.update_focus_group_simulation(
                simulation_id=simulation_id,
                user_id=user_id,
                update_data={"is_favorite": new_favorite_status}
            )

        except Exception as e:
            logger.error(f"Error toggling focus group favorite: {str(e)}")
            return None

    async def delete_focus_group_simulation(
        self,
        simulation_id: str,
        user_id: str
    ) -> bool:
        """
        Delete a focus group simulation

        Args:
            simulation_id: The simulation ID to delete
            user_id: The user ID (for security)

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            result = self.client.schema("api").table("focus_group_simulations").delete().eq("id", simulation_id).eq("user_id", user_id).execute()

            if result.data:
                logger.info(f"Successfully deleted focus group simulation {simulation_id} for user {user_id}")
                return True
            else:
                logger.warning(f"Focus group simulation {simulation_id} not found for deletion")
                return False

        except Exception as e:
            logger.error(f"Error deleting focus group simulation: {str(e)}")
            return False

    async def cleanup_old_focus_group_simulations(
        self,
        user_id: str,
        keep_recent: int = 5
    ) -> int:
        """
        Clean up old focus group simulations, keeping only the most recent ones
        (excluding favorites)

        Args:
            user_id: The user ID
            keep_recent: Number of recent simulations to keep

        Returns:
            Number of simulations deleted
        """
        try:
            # Get all non-favorite simulations ordered by creation date
            result = self.client.schema("api").table("focus_group_simulations").select("id, created_at").eq("user_id", user_id).eq("is_favorite", False).order("created_at", desc=True).execute()

            if not result.data or len(result.data) <= keep_recent:
                return 0

            # Get IDs of simulations to delete (skip the most recent ones)
            simulations_to_delete = result.data[keep_recent:]
            ids_to_delete = [sim["id"] for sim in simulations_to_delete]

            if not ids_to_delete:
                return 0

            # Delete the old simulations
            delete_result = self.client.schema("api").table("focus_group_simulations").delete().in_("id", ids_to_delete).execute()

            deleted_count = len(delete_result.data) if delete_result.data else 0
            logger.info(f"Cleaned up {deleted_count} old focus group simulations for user {user_id}")
            return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old focus group simulations: {str(e)}")
            return 0

    async def get_user_palettes(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get all palettes for a user

        Args:
            user_id: The ID of the user
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of palette records
        """
        try:
            result = self.client.schema("api").table("user_palettes").select("*").eq("user_id", user_id).order("updated_at", desc=True).limit(limit).offset(offset).execute()

            if result.data:
                logger.info(f"Retrieved {len(result.data)} palettes for user {user_id}")
                return result.data
            else:
                logger.info(f"No palettes found for user {user_id}")
                return []

        except Exception as e:
            logger.error(f"Error getting user palettes: {str(e)}")
            return []

    async def get_palette_by_id(
        self,
        palette_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific palette by ID (with user verification for security)

        Args:
            palette_id: The ID of the palette
            user_id: The ID of the user (for security check)

        Returns:
            The palette record or None if not found/not authorized
        """
        try:
            result = self.client.schema("api").table("user_palettes").select("*").eq("id", palette_id).eq("user_id", user_id).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"Retrieved palette {palette_id} for user {user_id}")
                return result.data[0]
            else:
                logger.warning(f"Palette {palette_id} not found or not authorized for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Error retrieving palette: {str(e)}")
            return None

    async def update_user_palette(
        self,
        palette_id: str,
        user_id: str,
        palette_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing user palette

        Args:
            palette_id: The ID of the palette to update
            user_id: The ID of the user (for security check)
            palette_data: Dictionary containing updated palette information

        Returns:
            The updated palette record or None if failed
        """
        try:
            # Prepare the data for update
            update_data = {}

            # Only update fields that are provided
            if "name" in palette_data:
                update_data["name"] = palette_data["name"]
            if "colors" in palette_data:
                update_data["colors"] = palette_data["colors"]
            if "description" in palette_data:
                update_data["description"] = palette_data["description"]
            if "tags" in palette_data:
                update_data["tags"] = palette_data["tags"]
            if "is_favorite" in palette_data:
                update_data["is_favorite"] = palette_data["is_favorite"]

            # Update in database
            result = self.client.schema("api").table("user_palettes").update(update_data).eq("id", palette_id).eq("user_id", user_id).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"Successfully updated palette {palette_id} for user {user_id}")
                return result.data[0]
            else:
                logger.warning(f"Palette {palette_id} not found or not authorized for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Error updating palette: {str(e)}")
            return None

    async def delete_user_palette(
        self,
        palette_id: str,
        user_id: str
    ) -> bool:
        """
        Delete a user palette (hard delete)

        Args:
            palette_id: The ID of the palette to delete
            user_id: The ID of the user (for security check)

        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.client.schema("api").table("user_palettes").delete().eq("id", palette_id).eq("user_id", user_id).execute()

            if result.data:
                logger.info(f"Successfully deleted palette {palette_id} for user {user_id}")
                return True
            else:
                logger.warning(f"Palette {palette_id} not found or not authorized for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting palette: {str(e)}")
            return False

    async def update_palette_favorite(
        self,
        palette_id: str,
        user_id: str,
        is_favorite: bool
    ) -> bool:
        """
        Update the favorite status of a palette

        Args:
            palette_id: The ID of the palette
            user_id: The ID of the user (for security check)
            is_favorite: New favorite status

        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.client.schema("api").table("user_palettes").update({
                "is_favorite": is_favorite
            }).eq("id", palette_id).eq("user_id", user_id).execute()

            if result.data:
                logger.info(f"Updated favorite status for palette {palette_id}")
                return True
            else:
                logger.error(f"Failed to update favorite status for palette {palette_id}")
                return False

        except Exception as e:
            logger.error(f"Error updating favorite status: {str(e)}")
            return False


# Global instance
supabase_service = SupabaseService()


def get_supabase_service() -> SupabaseService:
    """
    Dependency function to get the Supabase service instance
    """
    return supabase_service
