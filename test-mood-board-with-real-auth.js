/**
 * Test mood board creation with real authentication
 * Run this in the browser console on any Emma Studio page where you're logged in
 */

async function testMoodBoardWithRealAuth() {
  console.log('🔐 Testing Mood Board Creation with Real Authentication');
  console.log('====================================================');

  try {
    // Step 1: Get real authentication
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user, session }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !session) {
      console.error('❌ Authentication failed:', authError);
      console.log('💡 Please make sure you are logged in to Emma Studio');
      return;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      tokenLength: session.access_token?.length,
      tokenStart: session.access_token?.substring(0, 20) + '...'
    });

    // Step 2: Test the create endpoint
    const testData = {
      title: "Real Auth Test " + new Date().toISOString(),
      description: "Testing with real authentication",
      tldraw_data: {
        records: [],
        bindings: [],
        assets: []
      },
      tags: ["real-auth-test"],
      is_public: false,
      is_favorite: false,
      collaboration_enabled: false,
      shared_with: [],
      notes: "Real authentication test"
    };

    console.log('📤 Sending create request...');
    console.log('🔑 Using token:', session.access_token.substring(0, 50) + '...');
    
    const response = await fetch('http://localhost:8000/api/moodboard/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(testData)
    });

    console.log('📡 Response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error response:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        console.error('📋 Parsed error:', errorJson);
      } catch (e) {
        console.error('📋 Could not parse error as JSON');
      }
      return;
    }

    const result = await response.json();
    console.log('✅ Success!', result);
    
    if (result.success && result.data?.id) {
      console.log('🎉 Mood board created successfully!');
      console.log('📊 Created mood board:', {
        id: result.data.id,
        title: result.data.title,
        userId: result.data.user_id,
        createdAt: result.data.created_at
      });
      
      // Test retrieval
      console.log('\n🔍 Testing retrieval...');
      const getResponse = await fetch(`http://localhost:8000/api/moodboard/${result.data.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });
      
      if (getResponse.ok) {
        const getData = await getResponse.json();
        console.log('✅ Retrieval successful:', getData.data?.title);
      } else {
        console.error('❌ Retrieval failed:', getResponse.status);
      }
      
      // Refresh page to see the new mood board in the list
      console.log('\n🔄 Refreshing page in 3 seconds...');
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('📋 Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
  }
}

// Run the test
testMoodBoardWithRealAuth();
