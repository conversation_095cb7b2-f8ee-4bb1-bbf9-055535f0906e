/**
 * Comprehensive Image Retrieval Test Script
 * Tests all image retrieval methods with actual stored files
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🧪 Starting comprehensive image retrieval tests...');

async function testImageRetrievalMethods() {
  try {
    // Check if designAnalysisService is available
    if (typeof designAnalysisService === 'undefined') {
      console.error('❌ designAnalysisService not available. Make sure you are on the Visual Complexity Analyzer page.');
      return;
    }

    console.log('✅ designAnalysisService is available');

    // Test file paths from database
    const testFilePaths = [
      'test-debug-user-123/1234567890_abcd1234_debug-test.png',
      '8142a43b-add3-46ce-9eda-2d9b1dc81f56/some-test-file.jpg' // Current user path
    ];

    for (const filePath of testFilePaths) {
      console.log(`\n🔍 Testing image retrieval for: ${filePath}`);
      
      // Test 1: Primary method (getImageUrl)
      console.log('📋 Test 1: Primary method (getImageUrl)');
      try {
        const primaryUrl = await designAnalysisService.getImageUrl(filePath);
        if (primaryUrl) {
          console.log('✅ Primary method SUCCESS:', {
            type: primaryUrl.startsWith('blob:') ? 'Blob URL' : primaryUrl.startsWith('http') ? 'HTTP URL' : 'Unknown',
            url: primaryUrl.substring(0, 50) + '...'
          });
          
          // Test if the URL actually loads an image
          const imageLoadTest = await testImageLoad(primaryUrl);
          console.log(`🖼️ Image load test: ${imageLoadTest ? '✅ SUCCESS' : '❌ FAILED'}`);
          
          // Clean up blob URL
          if (primaryUrl.startsWith('blob:')) {
            URL.revokeObjectURL(primaryUrl);
          }
        } else {
          console.log('❌ Primary method returned null');
        }
      } catch (error) {
        console.error('💥 Primary method ERROR:', error);
      }

      // Test 2: Fallback method (getImageUrlFallback)
      console.log('📋 Test 2: Fallback method (getImageUrlFallback)');
      try {
        const fallbackUrl = await designAnalysisService.getImageUrlFallback(filePath);
        if (fallbackUrl) {
          console.log('✅ Fallback method SUCCESS:', {
            type: fallbackUrl.startsWith('blob:') ? 'Blob URL' : fallbackUrl.startsWith('http') ? 'HTTP URL' : 'Unknown',
            url: fallbackUrl.substring(0, 50) + '...'
          });
          
          // Test if the URL actually loads an image
          const imageLoadTest = await testImageLoad(fallbackUrl);
          console.log(`🖼️ Image load test: ${imageLoadTest ? '✅ SUCCESS' : '❌ FAILED'}`);
          
          // Clean up blob URL
          if (fallbackUrl.startsWith('blob:')) {
            URL.revokeObjectURL(fallbackUrl);
          }
        } else {
          console.log('❌ Fallback method returned null');
        }
      } catch (error) {
        console.error('💥 Fallback method ERROR:', error);
      }

      // Test 3: Smart method with fallback (getImageUrlWithFallback)
      console.log('📋 Test 3: Smart method with fallback (getImageUrlWithFallback)');
      try {
        const smartUrl = await designAnalysisService.getImageUrlWithFallback(filePath);
        if (smartUrl) {
          console.log('✅ Smart method SUCCESS:', {
            type: smartUrl.startsWith('blob:') ? 'Blob URL' : smartUrl.startsWith('http') ? 'HTTP URL' : 'Unknown',
            url: smartUrl.substring(0, 50) + '...'
          });
          
          // Test if the URL actually loads an image
          const imageLoadTest = await testImageLoad(smartUrl);
          console.log(`🖼️ Image load test: ${imageLoadTest ? '✅ SUCCESS' : '❌ FAILED'}`);
          
          // Clean up blob URL
          if (smartUrl.startsWith('blob:')) {
            URL.revokeObjectURL(smartUrl);
          }
        } else {
          console.log('❌ Smart method returned null');
        }
      } catch (error) {
        console.error('💥 Smart method ERROR:', error);
      }

      console.log('─'.repeat(50));
    }

    // Test 4: Test with actual analysis data
    console.log('\n🔍 Testing with actual analysis data...');
    try {
      const analyses = await designAnalysisService.getUserAnalyses({ limit: 10 });
      console.log(`📊 Found ${analyses.length} analyses`);
      
      const analysesWithFileUrl = analyses.filter(a => a.file_url);
      console.log(`📁 Found ${analysesWithFileUrl.length} analyses with file_url`);
      
      if (analysesWithFileUrl.length > 0) {
        const testAnalysis = analysesWithFileUrl[0];
        console.log('🧪 Testing with analysis:', {
          id: testAnalysis.id,
          filename: testAnalysis.original_filename,
          file_url: testAnalysis.file_url
        });
        
        const url = await designAnalysisService.getImageUrl(testAnalysis.file_url);
        if (url) {
          console.log('✅ Analysis image retrieval SUCCESS');
          const imageLoadTest = await testImageLoad(url);
          console.log(`🖼️ Analysis image load test: ${imageLoadTest ? '✅ SUCCESS' : '❌ FAILED'}`);
          
          if (url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
          }
        } else {
          console.log('❌ Analysis image retrieval FAILED');
        }
      } else {
        console.log('⚠️ No analyses with file_url found for testing');
      }
    } catch (error) {
      console.error('💥 Analysis data test ERROR:', error);
    }

    console.log('\n🎯 Image retrieval tests completed!');
    
  } catch (error) {
    console.error('💥 Test suite ERROR:', error);
  }
}

// Helper function to test if an image URL actually loads
function testImageLoad(url) {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
    
    // Timeout after 10 seconds
    setTimeout(() => resolve(false), 10000);
  });
}

// Run the tests
testImageRetrievalMethods();
